<Application xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             x:Class="Polymerium.App.App"
             RequestedThemeVariant="Default">
    <!-- "Default" ThemeVariant follows system theme variant. "Dark" or "Light" are other available options. -->
    <Application.Styles>
        <husk:HuskuiTheme />
        <StyleInclude Source="avares://IconPacks.Avalonia.Lucide/Lucide.axaml" />
        <StyleInclude Source="avares://Polymerium.App/Themes/Bundle.axaml" />
    </Application.Styles>
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.ThemeDictionaries>
                <ResourceDictionary x:Key="Light">
                    <ImageBrush x:Key="SettingsImageBrush"
                                Source="avares://Polymerium.App/Assets/Images/126997360_p2.jpg"
                                Stretch="UniformToFill" />
                    <ImageBrush x:Key="SettingsAiBadgeEnglishImageBrush"
                                Source="avares://Polymerium.App/Assets/Badges/Developed-By-Humans-Not-By-AI-Badge-white-EN.png" />
                    <ImageBrush x:Key="SettingsAiBadgeChineseImageBrush"
                                Source="avares://Polymerium.App/Assets/Badges/Developed-By-Human-Not-By-AI-Badge-white-CN.png" />
                </ResourceDictionary>
                <ResourceDictionary x:Key="Dark">
                    <ImageBrush x:Key="SettingsImageBrush"
                                Source="avares://Polymerium.App/Assets/Images/126997360_p0.jpg"
                                Stretch="UniformToFill" />
                    <ImageBrush x:Key="SettingsAiBadgeEnglishImageBrush"
                                Source="avares://Polymerium.App/Assets/Badges/Developed-By-Humans-Not-By-AI-Badge-black-EN.png" />
                    <ImageBrush x:Key="SettingsAiBadgeChineseImageBrush"
                                Source="avares://Polymerium.App/Assets/Badges/Developed-By-Human-Not-By-AI-Badge-black-CN.png" />
                </ResourceDictionary>
            </ResourceDictionary.ThemeDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>

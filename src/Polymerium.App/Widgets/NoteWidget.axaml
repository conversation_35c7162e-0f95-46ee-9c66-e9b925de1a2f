<local:WidgetBase xmlns="https://github.com/avaloniaui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:local="using:Polymerium.App.Widgets" Title="Note"
                  mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                  x:Class="Polymerium.App.Widgets.NoteWidget">
    <local:WidgetBase.FullTemplate>
        <DataTemplate x:DataType="local:NoteWidget">
            <TextBox Theme="{StaticResource FieldTextBoxTheme}" Text="{Binding NoteText,Mode=TwoWay}"
                     AcceptsReturn="True" AcceptsTab="True" />
        </DataTemplate>
    </local:WidgetBase.FullTemplate>
    <local:WidgetBase.SlimTemplate>
        <DataTemplate x:DataType="local:NoteWidget">
            <ScrollViewer>
                <TextBlock Text="{Binding NoteText}" TextWrapping="Wrap" />
            </ScrollViewer>
        </DataTemplate>
    </local:WidgetBase.SlimTemplate>
</local:WidgetBase>

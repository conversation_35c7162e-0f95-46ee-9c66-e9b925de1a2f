<controls:AccountCreationStep xmlns="https://github.com/avaloniaui"
                              xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                              xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                              xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                              xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                              xmlns:controls="clr-namespace:Polymerium.App.Controls"
                              xmlns:local="clr-namespace:Polymerium.App.Components"
                              xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
                              mc:Ignorable="d" d:DesignHeight="320" d:DesignWidth="256"
                              x:Class="Polymerium.App.Components.AccountCreationPortal"
                              Header="{x:Static lang:Resources.AccountCreationPortal_Title}"
                              IsNextAvailable="True">
    <StackPanel Spacing="12">
        <TabStrip Name="AccountTypeSelectBox">
            <TabStrip.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Spacing="6" />
                </ItemsPanelTemplate>
            </TabStrip.ItemsPanel>
            <TabStripItem>
                <StackPanel>
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_MicrosoftTitle}"
                               FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_MicrosoftSubtitle}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" TextWrapping="Wrap" />
                </StackPanel>
            </TabStripItem>
            <TabStripItem>
                <StackPanel>
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_TrialTitle}"
                               FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_TrialSubtitle}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" TextWrapping="Wrap" />
                </StackPanel>
            </TabStripItem>
            <TabStripItem
                IsEnabled="{Binding $parent[local:AccountCreationPortal].IsOfflineAvailable,FallbackValue=False}">
                <StackPanel>
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_OfflineTitle}"
                               FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock Text="{x:Static lang:Resources.AccountCreationPortal_OfflineSubtitle}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" TextWrapping="Wrap" />
                </StackPanel>
            </TabStripItem>
        </TabStrip>
        <husk:InfoBar
            IsVisible="{Binding $parent[local:AccountCreationPortal].IsOfflineAvailable,Converter={x:Static BoolConverters.Not},FallbackValue=True}"
            Content="{x:Static lang:Resources.AccountCreationPortal_Prompt}" />
    </StackPanel>
</controls:AccountCreationStep>

<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:m="using:Polymerium.App.Models"
             xmlns:cp="using:Polymerium.App.Components"
             xmlns:fie="using:FluentIcons.Avalonia.MarkupExtensions"
             xmlns:controls="clr-namespace:Polymerium.App.Controls"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:resources="clr-namespace:Trident.Abstractions.Repositories.Resources;assembly=Trident.Abstractions"
             xmlns:collections="clr-namespace:System.Collections.Generic;assembly=System.Collections"
             xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Polymerium.App.Components.PackageContainer">
    <UserControl.Resources>
        <MenuFlyout x:Key="InstancePackageButtonFlyout" x:DataType="m:InstancePackageModel">
            <MenuItem Header="{x:Static lang:Resources.PackageContainer_ActiveMenuText}"
                      IsChecked="{Binding IsEnabled,Mode=TwoWay}"
                      ToggleType="CheckBox"
                      Icon="{fie:SymbolIcon Symbol=Star,FontSize={StaticResource MediumFontSize}}" />
            <MenuItem Header="-" />
            <MenuItem Header="{x:Static lang:Resources.PackageContainer_OpenWebsiteMenuText}"
                      Command="{x:Static m:InternalCommands.OpenUriCommand}"
                      CommandParameter="{Binding Reference}"
                      Icon="{fie:SymbolIcon Symbol=Open,FontSize={StaticResource MediumFontSize}}" />
            <!-- <MenuItem Header="Add to Favorites" -->
            <!--           Icon="{fie:SymbolIcon Symbol=CollectionsAdd,FontSize={StaticResource MediumFontSize}}" /> -->
            <!-- <MenuItem Header="Remove from Favorite" -->
            <!--           Icon="{fie:SymbolIcon Symbol=Collections,FontSize={StaticResource MediumFontSize}}" /> -->
            <MenuItem Header="-" />
            <MenuItem Header="{x:Static lang:Resources.PackageContainer_RemoveMenuText}"
                      Command="{Binding $parent[cp:PackageContainer].RemoveCommand,Mode=OneWay}"
                      CommandParameter="{Binding }"
                      IsEnabled="{Binding IsLocked,Converter={x:Static BoolConverters.Not}}"
                      Icon="{fie:SymbolIcon Symbol=Delete,FontSize={StaticResource MediumFontSize}}" />
        </MenuFlyout>
    </UserControl.Resources>
    <Grid RowDefinitions="Auto,*" ColumnDefinitions="*,Auto">
        <Grid Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" ColumnDefinitions="Auto,*,Auto" Margin="16,8"
              ColumnSpacing="12">
            <TextBox Grid.Column="0" Watermark="{x:Static lang:Resources.PackageContainer_FilterBarPlaceholder}"
                     MinWidth="168"
                     Text="{Binding $parent[cp:PackageContainer].FilterText,Mode=OneWayToSource}"
                     ToolTip.Tip="<AUTHOR> !Id Names">
                <TextBox.InnerRightContent>
                    <StackPanel Orientation="Horizontal">
                        <Button
                            IsVisible="{Binding $parent[TextBox].Text,Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                            Command="{Binding $parent[TextBox].Clear}"
                            Content="{fie:SymbolIcon Symbol=Dismiss,FontSize={StaticResource MediumFontSize}}"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        <Button Classes.Primary="{Binding $parent[cp:PackageContainer].IsFilterActive,Mode=OneWay}">
                            <Button.Flyout>
                                <Flyout>
                                    <StackPanel Spacing="8" MinWidth="320" MaxWidth="520" MaxHeight="360" Margin="12">
                                        <TextBlock Text="{x:Static lang:Resources.PackageContainer_ConditionLabelText}"
                                                   FontSize="{StaticResource SmallFontSize}"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   VerticalAlignment="Center" />
                                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="24">
                                            <TextBlock Grid.Column="0"
                                                       Text="{x:Static lang:Resources.PackageContainer_StatusLabelText}"
                                                       VerticalAlignment="Bottom" />
                                            <TabStrip Grid.Column="1" Theme="{StaticResource SegmentedTabStripTheme}"
                                                      SelectedItem="{Binding $parent[cp:PackageContainer].FilterEnability,Mode=TwoWay}">
                                                <TabStrip.ItemTemplate>
                                                    <DataTemplate DataType="controls:PackageEntryEnabilityFilter">
                                                        <TextBlock Text="{Binding Label}" />
                                                    </DataTemplate>
                                                </TabStrip.ItemTemplate>
                                                <controls:PackageEntryEnabilityFilter
                                                    Label="{x:Static lang:Resources.Enum_All}" Enability="{x:Null}" />
                                                <controls:PackageEntryEnabilityFilter
                                                    Label="{x:Static lang:Resources.Enum_Enabled}"
                                                    Enability="{x:True}" />
                                                <controls:PackageEntryEnabilityFilter
                                                    Label="{x:Static lang:Resources.Enum_Disabled}"
                                                    Enability="{x:False}" />
                                            </TabStrip>
                                        </Grid>
                                        <husk:Divider />
                                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="24">
                                            <TextBlock Grid.Column="0"
                                                       Text="{x:Static lang:Resources.PackageContainer_SourceLabelText}"
                                                       VerticalAlignment="Bottom" />
                                            <TabStrip Grid.Column="1" Theme="{StaticResource SegmentedTabStripTheme}"
                                                      SelectedItem="{Binding $parent[cp:PackageContainer].FilterLockility,Mode=TwoWay}">
                                                <TabStrip.ItemTemplate>
                                                    <DataTemplate DataType="controls:PackageEntryLockilityFilter">
                                                        <TextBlock Text="{Binding Label}" />
                                                    </DataTemplate>
                                                </TabStrip.ItemTemplate>
                                                <controls:PackageEntryLockilityFilter
                                                    Label="{x:Static lang:Resources.Enum_All}" Lockility="{x:Null}" />
                                                <controls:PackageEntryLockilityFilter
                                                    Label="{x:Static lang:Resources.PackageContainer_SourceOriginalText}"
                                                    Lockility="{x:True}" />
                                                <controls:PackageEntryLockilityFilter
                                                    Label="{x:Static lang:Resources.PackageContainer_SourceLocalText}"
                                                    Lockility="{x:False}" />
                                            </TabStrip>
                                        </Grid>
                                        <husk:Divider />
                                        <Grid ColumnDefinitions="*,Auto" ColumnSpacing="24">
                                            <TextBlock Grid.Column="0"
                                                       Text="{x:Static lang:Resources.PackageContainer_TypeLabelText}"
                                                       VerticalAlignment="Bottom" />
                                            <TabStrip Grid.Column=" 1" Theme="{StaticResource SegmentedTabStripTheme}"
                                                      SelectedItem="{Binding $parent[cp:PackageContainer].FilterKind,Mode=TwoWay}">
                                                <TabStrip.ItemTemplate>
                                                    <DataTemplate DataType="controls:PackageEntryKindFilter">
                                                        <TextBlock Text="{Binding Label}" />
                                                    </DataTemplate>
                                                </TabStrip.ItemTemplate>
                                                <controls:PackageEntryKindFilter
                                                    Label="{x:Static lang:Resources.Enum_All}" Kind="{x:Null}" />
                                                <controls:PackageEntryKindFilter
                                                    Label="{x:Static lang:Resources.ResourceKind_Mod}"
                                                    Kind="{Binding Source=Mod,Converter={x:Static DefaultValueConverter.Instance},x:DataType=resources:ResourceKind}" />
                                                <controls:PackageEntryKindFilter
                                                    Label="{x:Static lang:Resources.ResourceKind_ResourcePack}"
                                                    Kind="{Binding Source=ResourcePack,Converter={x:Static DefaultValueConverter.Instance},x:DataType=resources:ResourceKind}" />
                                                <controls:PackageEntryKindFilter
                                                    Label="{x:Static lang:Resources.ResourceKind_ShaderPack}"
                                                    Kind="{Binding Source=ShaderPack,Converter={x:Static DefaultValueConverter.Instance},x:DataType=resources:ResourceKind}" />
                                                <controls:PackageEntryKindFilter
                                                    Label="{x:Static lang:Resources.ResourceKind_DataPack}"
                                                    Kind="{Binding Source=DataPack,Converter={x:Static DefaultValueConverter.Instance},x:DataType=resources:ResourceKind}" />
                                            </TabStrip>
                                        </Grid>
                                        <husk:Divider />
                                        <Grid ColumnDefinitions="*,Auto,Auto" ColumnSpacing="4">
                                            <TextBlock Grid.Column="0"
                                                       Text="{x:Static lang:Resources.PackageContainer_TagLabelText}"
                                                       VerticalAlignment="Center"
                                                       FontSize="{StaticResource SmallFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        </Grid>
                                        <Panel MinHeight="120">
                                            <StackPanel VerticalAlignment="Center"
                                                        Spacing="8"
                                                        IsVisible="{Binding $parent[cp:PackageContainer].TagsView.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}">
                                                <icons:PackIconLucide Kind="Tags"
                                                                      Height="{StaticResource ExtraLargeFontSize}"
                                                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                      Width="{StaticResource ExtraLargeFontSize}"
                                                                      HorizontalAlignment="Center" />
                                                <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                                                           FontSize="{StaticResource LargeFontSize}"
                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                           HorizontalAlignment="Center" />
                                            </StackPanel>
                                            <ItemsControl
                                                ItemsSource="{Binding $parent[cp:PackageContainer].TagsView}">
                                                <ItemsControl.ItemsPanel>
                                                    <ItemsPanelTemplate>
                                                        <WrapPanel ItemSpacing="8" LineSpacing="8" />
                                                    </ItemsPanelTemplate>
                                                </ItemsControl.ItemsPanel>
                                                <ItemsControl.ItemTemplate>
                                                    <DataTemplate x:DataType="m:InstancePackageFilterTagModel">
                                                        <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                                                BorderBrush="{StaticResource ControlBorderBrush}"
                                                                BorderThickness="1" Padding="8">
                                                            <CheckBox Content="{Binding Content}"
                                                                      IsChecked="{Binding IsSelected,Mode=TwoWay}" />
                                                        </Border>
                                                    </DataTemplate>
                                                </ItemsControl.ItemTemplate>
                                            </ItemsControl>
                                        </Panel>
                                    </StackPanel>
                                </Flyout>
                            </Button.Flyout>
                            <icons:PackIconLucide Kind="ListFilter" Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" />
                        </Button>
                    </StackPanel>
                </TextBox.InnerRightContent>
            </TextBox>
            <TextBlock Grid.Column="1">
                <Run Text="{x:Static lang:Resources.PackageContainer_ResultCountLabelText}"
                     Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                <LineBreak />
                <Run Text="{Binding $parent[cp:PackageContainer].View.Count,Mode=OneWay,FallbackValue=0}" />
                <Run Text="/" Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                <Run Text="{Binding $parent[cp:PackageContainer].TotalCount,Mode=OneWay,FallbackValue=0}" />
            </TextBlock>
            <StackPanel Grid.Column="2" DockPanel.Dock="Right" Orientation="Horizontal" Spacing="12">
                <TabStrip Name="LayoutSelector" Theme="{StaticResource SegmentedTabStripTheme}"
                          SelectedIndex="{Binding $parent[cp:PackageContainer].LayoutIndex,Mode=TwoWay}">
                    <TabStrip.ItemsSource>
                        <collections:List x:TypeArguments="m:ListTemplateCombinationModel">
                            <m:ListTemplateCombinationModel
                                Icon="LayoutList">
                                <m:ListTemplateCombinationModel.ItemTemplate>
                                    <DataTemplate x:DataType="m:InstancePackageModel">
                                        <controls:InstancePackageButton
                                            Margin="0,3"
                                            ContextFlyout="{StaticResource InstancePackageButtonFlyout}"
                                            Theme="{StaticResource ListPackageEntryButtonTheme}"
                                            Command="{Binding PrimaryCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=cp:PackageContainer}}"
                                            CommandParameter="{Binding}" />
                                    </DataTemplate>
                                </m:ListTemplateCombinationModel.ItemTemplate>
                                <m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                    <ItemsPanelTemplate>
                                        <VirtualizingStackPanel />
                                    </ItemsPanelTemplate>
                                </m:ListTemplateCombinationModel.ItemsPanelTemplate>
                            </m:ListTemplateCombinationModel>
                            <m:ListTemplateCombinationModel
                                Icon="LayoutGrid">
                                <m:ListTemplateCombinationModel.ItemTemplate>
                                    <DataTemplate x:DataType="m:InstancePackageModel">
                                        <controls:InstancePackageButton
                                            ContextFlyout="{StaticResource InstancePackageButtonFlyout}"
                                            ToolTip.Tip="{Binding ProjectName}"
                                            Theme="{StaticResource GridPackageEntryButtonTheme}"
                                            Command="{Binding PrimaryCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=cp:PackageContainer}}"
                                            CommandParameter="{Binding}" />
                                    </DataTemplate>
                                </m:ListTemplateCombinationModel.ItemTemplate>
                                <m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                    <ItemsPanelTemplate>
                                        <husk:FlexWrapPanel ColumnSpacing="6" RowSpacing="6" />
                                    </ItemsPanelTemplate>
                                </m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                <m:ListTemplateCombinationModel.ItemContainerTheme>
                                    <ControlTheme TargetType="ContentPresenter">
                                        <Setter Property="MaxWidth" Value="144" />
                                        <Setter Property="MinWidth" Value="90" />
                                        <Setter Property="MaxHeight" Value="144" />
                                        <Setter Property="MinHeight" Value="90" />
                                    </ControlTheme>
                                </m:ListTemplateCombinationModel.ItemContainerTheme>
                            </m:ListTemplateCombinationModel>
                        </collections:List>
                    </TabStrip.ItemsSource>
                    <TabStrip.ItemTemplate>
                        <DataTemplate x:DataType="m:ListTemplateCombinationModel">
                            <icons:PackIconLucide Kind="{Binding Icon}"
                                                  Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}"
                                                  VerticalAlignment="Center" HorizontalAlignment="Center" />
                        </DataTemplate>
                    </TabStrip.ItemTemplate>
                </TabStrip>
                <SplitButton Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                             Foreground="{StaticResource ControlBlackForegroundBrush}"
                             Command="{Binding $parent[cp:PackageContainer].GotoExplorerCommand}">
                    <SplitButton.Flyout>
                        <MenuFlyout>
                            <MenuItem Header="{x:Static lang:Resources.PackageContainer_BatchUpdateMenuText}"
                                      Icon="{fie:SymbolIcon Symbol=ArrowForward,FontSize={StaticResource MediumFontSize}}"
                                      Command="{Binding $parent[cp:PackageContainer].UpdateBatchCommand}"
                                      CommandParameter="{Binding $parent[cp:PackageContainer].Items}" />
                            <MenuItem Header="{x:Static lang:Resources.PackageContainer_DependencyGraphMenuText}"
                                      Icon="{fie:SymbolIcon Symbol=ArrowSplit,FontSize={StaticResource MediumFontSize}}"
                                      IsEnabled="False" />
                            <MenuItem Header="{x:Static lang:Resources.PackageContainer_ExportListMenuText}"
                                      Icon="{fie:SymbolIcon Symbol=ArrowExport,FontSize={StaticResource MediumFontSize}}"
                                      Command="{Binding $parent[cp:PackageContainer].ExportListCommand}" />
                        </MenuFlyout>
                    </SplitButton.Flyout>
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <icons:PackIconLucide Kind="WandSparkles" Height="{StaticResource MediumFontSize}"
                                              VerticalAlignment="Center" />
                        <TextBlock Text="{x:Static lang:Resources.PackageContainer_GetMoreButtonText}" />
                    </StackPanel>
                </SplitButton>
            </StackPanel>
        </Grid>
        <Panel Grid.Column="0" Grid.Row="1">
            <StackPanel VerticalAlignment="Center"
                        IsVisible="{Binding $parent[cp:PackageContainer].View.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}"
                        Spacing="8">
                <icons:PackIconLucide Kind="Package" Height="{StaticResource ExtraLargeFontSize}"
                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                      Width="{StaticResource ExtraLargeFontSize}" HorizontalAlignment="Center" />
                <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                           FontSize="{StaticResource LargeFontSize}"
                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" HorizontalAlignment="Center" />
            </StackPanel>
            <ScrollViewer>
                <ItemsControl Margin="{StaticResource PageToplessContentMargin}"
                              ItemsSource="{Binding $parent[cp:PackageContainer].View}"
                              ItemTemplate="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemTemplate,FallbackValue={x:Null}}"
                              ItemsPanel="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemsPanelTemplate,FallbackValue={x:Null}}"
                              ItemContainerTheme="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemContainerTheme,FallbackValue={x:Null}}" />
            </ScrollViewer>
        </Panel>
        <!-- <Border Grid.Row="1" Grid.Column="1" Padding="12" -->
        <!--         Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"> -->
        <!--     <Grid RowDefinitions="Auto,*,Auto" RowSpan="24"> -->
        <!--         <Border Grid.Row="0" Padding="12,6" CornerRadius="{StaticResource MediumCornerRadius}" -->
        <!--                 Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"> -->
        <!--             <StackPanel Orientation="Horizontal" Spacing="6"> -->
        <!--                 <fi:SymbolIcon Symbol="Drafts" FontSize="{StaticResource LargeFontSize}" /> -->
        <!--                 <TextBlock Text="Drafts" VerticalAlignment="Center" /> -->
        <!--             </StackPanel> -->
        <!--         </Border> -->
        <!--         <ScrollViewer Grid.Row="1"> -->
        <!--             <StackPanel Spacing="8"> -->
        <!--                 <husk:Divider> -->
        <!--                     <StackPanel Orientation="Horizontal" Spacing="6"> -->
        <!--                         <TextBlock Text="Add" /> -->
        <!--                     </StackPanel> -->
        <!--                 </husk:Divider> -->
        <!--                 <Panel> -->
        <!--                     <TextBlock Text="Empty" Opacity="0.5" HorizontalAlignment="Center" Margin="8" -->
        <!--                                IsVisible="{Binding AddingPackagesView.Count,Mode=OneWay,Converter={x:Static huskc:NumberConverters.IsZero},FallbackValue=False}" /> -->
        <!--                     <ItemsControl ItemsSource="{Binding AddingPackagesView}" -->
        <!--                                   ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False"> -->
        <!--                         <ItemsControl.ItemsPanel> -->
        <!--                             <ItemsPanelTemplate> -->
        <!--                                 <StackPanel Spacing="8" /> -->
        <!--                             </ItemsPanelTemplate> -->
        <!--                         </ItemsControl.ItemsPanel> -->
        <!--                     </ItemsControl> -->
        <!--                 </Panel> -->
        <!--                 <husk:Divider> -->
        <!--                     <StackPanel Orientation="Horizontal" Spacing="6"> -->
        <!--                         <TextBlock Text="Modify" /> -->
        <!--                     </StackPanel> -->
        <!--                 </husk:Divider> -->
        <!--                 <Panel> -->
        <!--                     <TextBlock Text="Empty" Opacity="0.5" HorizontalAlignment="Center" Margin="8" -->
        <!--                                IsVisible="{Binding ModifyingPackagesView.Count,Mode=OneWay,Converter={x:Static huskc:NumberConverters.IsZero},FallbackValue=False}" /> -->
        <!--                     <ItemsControl ItemsSource="{Binding ModifyingPackagesView}" -->
        <!--                                   ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False"> -->
        <!--                         <ItemsControl.ItemsPanel> -->
        <!--                             <ItemsPanelTemplate> -->
        <!--                                 <StackPanel Spacing="8" /> -->
        <!--                             </ItemsPanelTemplate> -->
        <!--                         </ItemsControl.ItemsPanel> -->
        <!--                     </ItemsControl> -->
        <!--                 </Panel> -->
        <!--                 <husk:Divider> -->
        <!--                     <StackPanel Orientation="Horizontal" Spacing="6"> -->
        <!--                         <TextBlock Text="Remove" /> -->
        <!--                     </StackPanel> -->
        <!--                 </husk:Divider> -->
        <!--                 <Panel> -->
        <!--                     <TextBlock Text="Empty" Opacity="0.5" HorizontalAlignment="Center" Margin="8" -->
        <!--                                IsVisible="{Binding RemovingPackagesView.Count,Mode=OneWay,Converter={x:Static huskc:NumberConverters.IsZero},FallbackValue=False}" /> -->
        <!--                     <ItemsControl ItemsSource="{Binding RemovingPackagesView}" -->
        <!--                                   ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False"> -->
        <!--                         <ItemsControl.ItemsPanel> -->
        <!--                             <ItemsPanelTemplate> -->
        <!--                                 <StackPanel Spacing="8" /> -->
        <!--                             </ItemsPanelTemplate> -->
        <!--                         </ItemsControl.ItemsPanel> -->
        <!--                     </ItemsControl> -->
        <!--                 </Panel> -->
        <!--             </StackPanel> -->
        <!--         </ScrollViewer> -->
        <!--     </Grid> -->
        <!-- </Border> -->
    </Grid>
</UserControl>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="using:Polymerium.App.Controls"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <local:Subpage Width="128" Height="128" />
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type local:Subpage}" TargetType="local:Subpage">
        <Setter Property="Padding" Value="12" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="CanGoBack" Value="{Binding $parent[husk:Frame].CanGoBack, Mode=OneWay}" />
        <Setter Property="ClipToBounds" Value="True" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        Background="{TemplateBinding Background}" BackgroundSizing="{TemplateBinding BackgroundSizing}"
                        BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}"
                        ClipToBounds="True">
                    <ScrollViewer
                        VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}"
                        HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}"
                        IsDeferredScrollingEnabled="{TemplateBinding ScrollViewer.IsDeferredScrollingEnabled}"
                        IsScrollChainingEnabled="{TemplateBinding ScrollViewer.IsScrollChainingEnabled}"
                        IsScrollInertiaEnabled="{TemplateBinding ScrollViewer.IsScrollInertiaEnabled}"
                        BringIntoViewOnFocusChange="{TemplateBinding ScrollViewer.BringIntoViewOnFocusChange}">
                        <Panel>
                            <ContentPresenter Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
                        </Panel>
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceInclude Source="/Controls/Page.axaml" />
        <ResourceInclude Source="/Controls/ScopedPage.axaml" />
        <ResourceInclude Source="/Controls/Subpage.axaml" />
        <ResourceInclude Source="/Controls/LaunchBar.axaml" />
        <ResourceInclude Source="/Controls/InstanceEntryButton.axaml" />
        <ResourceInclude Source="/Controls/InstancePackageButton.axaml" />
        <ResourceInclude Source="/Controls/ExhibitModpackButton.axaml" />
        <ResourceInclude Source="/Controls/ExhibitPackageButton.axaml" />
        <ResourceInclude Source="/Controls/ExhibitPendingPackageButton.axaml" />
        <ResourceInclude Source="/Controls/SettingsEntry.axaml" />
        <ResourceInclude Source="/Controls/SettingsEntryItem.axaml" />
        <ResourceInclude Source="/Controls/ExhibitDependencyButton.axaml" />
        <ResourceInclude Source="/Controls/AccountEntryButton.axaml" />
        <ResourceInclude Source="/Controls/AccountCreationStep.axaml" />
        <ResourceInclude Source="/Controls/Plaque.axaml" />
        <ResourceInclude Source="/Controls/StorageInstanceButton.axaml" />
        <ResourceInclude Source="/Controls/InstancePackageDependencyButton.axaml" />
        <ResourceInclude Source="/Controls/InstanceActionCard.axaml" />
    </ResourceDictionary.MergedDictionaries>
</ResourceDictionary>

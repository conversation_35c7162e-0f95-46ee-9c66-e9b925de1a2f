<controls:ScopedPage xmlns="https://github.com/avaloniaui"
                     xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                     xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                     xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                     xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                     xmlns:controls="using:Polymerium.App.Controls"
                     xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                     xmlns:fie="clr-namespace:FluentIcons.Avalonia.MarkupExtensions;assembly=FluentIcons.Avalonia"
                     xmlns:vm="clr-namespace:Polymerium.App.ViewModels"
                     xmlns:m="clr-namespace:Polymerium.App.Models"
                     xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                     xmlns:v="clr-namespace:Polymerium.App.Views"
                     xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
                     xmlns:converters="clr-namespace:Polymerium.App.Converters"
                     xmlns:resources="clr-namespace:Trident.Abstractions.Repositories.Resources;assembly=Trident.Abstractions"
                     mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="450"
                     x:Class="Polymerium.App.Views.PackageExplorerView" x:DataType="vm:PackageExplorerViewModel"
                     Padding="0" IsHeaderVisible="False" ScrollViewer.VerticalScrollBarVisibility="Disabled">
    <controls:ScopedPage.Resources>
        <DataTemplate x:Key="ExhibitModelTemplate" x:DataType="m:ExhibitModel">
            <controls:ExhibitPendingPackageButton
                Command="{Binding $parent[v:PackageExplorerView].((vm:PackageExplorerViewModel)DataContext).ViewPackageCommand,FallbackValue={x:Null}}"
                CommandParameter="{Binding}" />
        </DataTemplate>
    </controls:ScopedPage.Resources>
    <Grid RowDefinitions="Auto,Auto,*" ColumnDefinitions="Auto,Auto,*">
        <Grid Grid.Column="0" Grid.Row="0" Grid.RowSpan="3" RowDefinitions="Auto,*,Auto" RowSpacing="12">
            <Border Grid.Row="0" Padding="12,6" CornerRadius="{StaticResource MediumCornerRadius}"
                    Background="{StaticResource ControlTranslucentHalfBackgroundBrush}" Margin="12">
                <StackPanel Orientation="Horizontal" Spacing="6">
                    <fi:SymbolIcon Symbol="Drawer" FontSize="{StaticResource LargeFontSize}" />
                    <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_PendingLabelText}"
                               VerticalAlignment="Center" />
                </StackPanel>
            </Border>
            <ScrollViewer Grid.Row="1" Padding="12,0">
                <StackPanel Spacing="8">
                    <husk:Divider>
                        <StackPanel Orientation="Horizontal" Spacing="6">
                            <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_AddLabelText}" />
                        </StackPanel>
                    </husk:Divider>
                    <Panel>
                        <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_EmptyLabelText}" Opacity="0.5"
                                   HorizontalAlignment="Center" Margin="8"
                                   IsVisible="{Binding AddingPackagesView.Count,Mode=OneWay,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=False}" />
                        <ItemsControl ItemsSource="{Binding AddingPackagesView}"
                                      ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="8" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </Panel>
                    <husk:Divider>
                        <StackPanel Orientation="Horizontal" Spacing="6">
                            <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_ModifyLabelText}" />
                        </StackPanel>
                    </husk:Divider>
                    <Panel>
                        <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_EmptyLabelText}" Opacity="0.5"
                                   HorizontalAlignment="Center" Margin="8"
                                   IsVisible="{Binding ModifyingPackagesView.Count,Mode=OneWay,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=False}" />
                        <ItemsControl ItemsSource="{Binding ModifyingPackagesView}"
                                      ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="8" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </Panel>
                    <husk:Divider>
                        <StackPanel Orientation="Horizontal" Spacing="6">
                            <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_RemoveLabelText}" />
                        </StackPanel>
                    </husk:Divider>
                    <Panel>
                        <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_EmptyLabelText}" Opacity="0.5"
                                   HorizontalAlignment="Center" Margin="8"
                                   IsVisible="{Binding RemovingPackagesView.Count,Mode=OneWay,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=False}" />
                        <ItemsControl ItemsSource="{Binding RemovingPackagesView}"
                                      ItemTemplate="{StaticResource ExhibitModelTemplate}" ClipToBounds="False">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="8" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </Panel>
                </StackPanel>
            </ScrollViewer>
            <StackPanel Grid.Row="2" Spacing="10" Margin="12">
                <Button Padding="12,6" CornerRadius="{StaticResource MediumCornerRadius}" Classes="Primary"
                        Command="{Binding CollectPendingCommand}">
                    <Button.IsEnabled>
                        <MultiBinding Converter="{x:Static BoolConverters.Or}">
                            <Binding Path="AddingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                            <Binding Path="ModifyingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                            <Binding Path="RemovingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                        </MultiBinding>
                    </Button.IsEnabled>
                    <StackPanel Orientation="Horizontal" Spacing="6">
                        <fi:SymbolIcon Symbol="BoxMultipleArrowLeft" FontSize="{StaticResource LargeFontSize}" />
                        <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_CollectButtonText}" />
                    </StackPanel>
                </Button>
                <Button Padding="12,6" CornerRadius="{StaticResource MediumCornerRadius}"
                        Command="{Binding DismissPendingCommand}">
                    <Button.IsEnabled>
                        <MultiBinding Converter="{x:Static BoolConverters.Or}">
                            <Binding Path="AddingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                            <Binding Path="ModifyingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                            <Binding Path="RemovingPackagesView.Count"
                                     Converter="{x:Static husk:NumberConverters.IsNonZero}" />
                        </MultiBinding>
                    </Button.IsEnabled>
                    <StackPanel Orientation="Horizontal" Spacing="6">
                        <fi:SymbolIcon Symbol="DrawerDismiss" FontSize="{StaticResource LargeFontSize}" />
                        <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_DismissButtonText}" />
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
        <husk:Divider Grid.Column="1" Grid.Row="0" Grid.RowSpan="3" Orientation="Vertical" />
        <Panel Grid.Column="2" Grid.Row="0" ClipToBounds="True">
            <Image Source="{Binding Basic.Thumbnail}" Height="{Binding #ContentPanel.Bounds.Height}" Stretch="Fill"
                   Opacity="0.2" RenderTransform="scale(1.1)">
                <Image.Effect>
                    <BlurEffect Radius="32" />
                </Image.Effect>
            </Image>
            <Panel x:Name="ContentPanel">
                <StackPanel Margin="12" Spacing="12">
                    <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                        <Button Grid.Column="0" Command="{Binding $parent[husk:Frame].GoBackCommand}"
                                Theme="{StaticResource OutlineButtonTheme}">
                            <fi:SymbolIcon Symbol="ArrowLeft" FontSize="{StaticResource MediumFontSize}" />
                        </Button>
                        <TextBlock Grid.Column="1" Text="{Binding Basic.Name,FallbackValue=Name}"
                                   TextTrimming="CharacterEllipsis"
                                   FontSize="{StaticResource ExtraLargeFontSize}" />
                    </Grid>
                    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="8">
                        <TextBox Grid.Column="0"
                                 Watermark="{x:Static lang:Resources.PackageExplorerView_SearchBarPlaceholder}"
                                 Text="{Binding QueryText}">
                            <TextBox.InnerRightContent>
                                <StackPanel Orientation="Horizontal">
                                    <Button
                                        IsVisible="{Binding $parent[TextBox].Text,Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                                        Command="{Binding $parent[TextBox].Clear}"
                                        Content="{fie:SymbolIcon Symbol=Dismiss,FontSize={StaticResource MediumFontSize}}"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                    <husk:Divider Orientation="Vertical" />
                                    <CheckBox Margin="6,0" IsChecked="{Binding IsFilterEnabled,Mode=TwoWay}">
                                        <StackPanel Spacing="4" Orientation="Horizontal">
                                            <husk:Tag Content="{Binding Basic.LoaderLabel,FallbackValue=Type}"
                                                      FontSize="{StaticResource MediumFontSize}" />
                                            <husk:Tag Content="{Binding Basic.Version,FallbackValue=Version}"
                                                      FontSize="{StaticResource MediumFontSize}" />
                                        </StackPanel>
                                    </CheckBox>
                                </StackPanel>
                            </TextBox.InnerRightContent>
                        </TextBox>
                        <Button Grid.Column="1" Classes="Primary" IsDefault="True"
                                Command="{Binding SearchCommand}"
                                Theme="{StaticResource OutlineButtonTheme}">
                            <StackPanel Orientation="Horizontal" Spacing="6">
                                <husk:SwitchPresenter VerticalAlignment="Center"
                                                      Value="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=True}"
                                                      TargetType="x:Boolean"
                                                      Height="{StaticResource MediumFontSize}"
                                                      Width="{StaticResource MediumFontSize}">
                                    <husk:SwitchCase Value="True">
                                        <husk:ProgressRing
                                            Theme="{StaticResource SolidProgressRingTheme}"
                                            IsIndeterminate="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=True}"
                                            StrokeWidth="2" TrackStrokeWidth="2" />
                                    </husk:SwitchCase>
                                    <husk:SwitchCase Value="False">
                                        <icons:PackIconLucide Kind="Search" />
                                    </husk:SwitchCase>
                                </husk:SwitchPresenter>
                                <TextBlock Text="{x:Static lang:Resources.PackageExplorerView_SearchButtonText}"
                                           VerticalAlignment="Center" />
                            </StackPanel>
                        </Button>
                    </Grid>
                    <Grid ColumnDefinitions="*,Auto">
                        <husk:SkeletonContainer Grid.Column="1" MinHeight="33" MinWidth="128"
                                                CornerRadius="{StaticResource SmallCornerRadius}"
                                                IsLoading="{Binding SelectedRepository.Kinds,Converter={x:Static ObjectConverters.IsNull}}">
                            <TabStrip
                                Theme="{StaticResource SegmentedTabStripTheme}"
                                SelectedItem="{Binding SelectedKind,Mode=TwoWay}"
                                ItemsSource="{Binding SelectedRepository.Kinds,Mode=OneWay}">
                                <TabStrip.ItemTemplate>
                                    <DataTemplate x:DataType="resources:ResourceKind">
                                        <TextBlock
                                            Text="{Binding Converter={x:Static converters:InternalConverters.LocalizedResourceKindConverter}}" />
                                    </DataTemplate>
                                </TabStrip.ItemTemplate>
                            </TabStrip>
                        </husk:SkeletonContainer>
                    </Grid>
                </StackPanel>
                <TabStrip HorizontalAlignment="Left" VerticalAlignment="Bottom"
                          SelectedItem="{Binding SelectedRepository}" ItemsSource="{Binding Repositories}"
                          Theme="{StaticResource PivotTabStripTheme}">
                    <TabStrip.Styles>
                        <Style Selector="TabStripItem">
                            <Setter Property="Margin" Value="0" />
                        </Style>
                    </TabStrip.Styles>
                    <TabStrip.ItemTemplate>
                        <DataTemplate x:DataType="m:RepositoryBasicModel">
                            <TextBlock Text="{Binding Name}" />
                        </DataTemplate>
                    </TabStrip.ItemTemplate>
                </TabStrip>
            </Panel>
        </Panel>
        <husk:Divider Grid.Column="2" Grid.Row="1" />
        <Panel Grid.Column="2" Grid.Row="2">
            <StackPanel VerticalAlignment="Center"
                        Spacing="8">
                <StackPanel.IsVisible>
                    <MultiBinding Converter="{x:Static BoolConverters.And}">
                        <Binding Path="Exhibits.IsFetching" Mode="OneWay"
                                 Converter="{x:Static BoolConverters.Not}"
                                 FallbackValue="{x:True}" />
                        <Binding Path="Exhibits.Count" Mode="OneWay"
                                 Converter="{x:Static husk:NumberConverters.IsZero}"
                                 FallbackValue="{x:True}" />
                    </MultiBinding>
                </StackPanel.IsVisible>
                <icons:PackIconLucide Kind="Package" Height="{StaticResource ExtraLargeFontSize}"
                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                      Width="{StaticResource ExtraLargeFontSize}" HorizontalAlignment="Center" />
                <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                           FontSize="{StaticResource LargeFontSize}"
                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                           HorizontalAlignment="Center" />
            </StackPanel>
            <husk:InfiniteScrollView Padding="12" ItemsSource="{Binding Exhibits}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <husk:FlexWrapPanel ColumnSpacing="8" RowSpacing="8" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <husk:InfiniteScrollView.PendingContent>
                    <StackPanel Margin="36" Spacing="6">
                        <husk:ProgressRing Width="20" Height="20"
                                           IsIndeterminate="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=False}"
                                           HorizontalAlignment="Center" ShowProgressText="False" />
                        <TextBlock Text="{x:Static lang:Resources.Shared_FetchingLabelText}"
                                   HorizontalAlignment="Center" />
                    </StackPanel>
                </husk:InfiniteScrollView.PendingContent>
                <husk:InfiniteScrollView.ItemTemplate>
                    <DataTemplate x:DataType="m:ExhibitModel">
                        <controls:ExhibitPackageButton
                            Command="{Binding $parent[v:PackageExplorerView].((vm:PackageExplorerViewModel)DataContext).ViewPackageCommand,FallbackValue={x:Null}}"
                            CommandParameter="{Binding}" />
                    </DataTemplate>
                </husk:InfiniteScrollView.ItemTemplate>
                <husk:InfiniteScrollView.ItemContainerTheme>
                    <ControlTheme TargetType="ContentPresenter">
                        <Setter Property="MaxWidth" Value="128" />
                        <Setter Property="MinWidth" Value="90" />
                        <Setter Property="MaxHeight" Value="168" />
                        <Setter Property="MinHeight" Value="128" />
                    </ControlTheme>
                </husk:InfiniteScrollView.ItemContainerTheme>
            </husk:InfiniteScrollView>
        </Panel>
    </Grid>
</controls:ScopedPage>

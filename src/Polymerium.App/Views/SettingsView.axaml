<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
           xmlns:controls="using:Polymerium.App.Controls"
           xmlns:vm="using:Polymerium.App.ViewModels"
           xmlns:m="using:Polymerium.App.Models"
           xmlns:cp="clr-namespace:Polymerium.App.Components"
           xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
           xmlns:v="clr-namespace:Polymerium.App.Views"
           xmlns:app="clr-namespace:Polymerium.App"
           xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
           x:Class="Polymerium.App.Views.SettingsView" Header="{x:Static lang:Resources.SettingsView_Title}"
           x:DataType="vm:SettingsViewModel">
    <StackPanel MaxWidth="1440" Spacing="12" Margin="24">
        <controls:SettingsEntry Title="{x:Static lang:Resources.SettingsView_SuperPowerTitle}" Icon="Wand"
                                Summary="{x:Static lang:Resources.SettingsView_SuperPowerSubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_SuperPowerLabelText}">
                <ToggleSwitch IsChecked="{Binding SuperPowerActivated,Mode=TwoWay}"
                              OnContent="{x:Static lang:Resources.Enum_On}"
                              OffContent="{x:Static lang:Resources.Enum_Off}" />
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="LocalLanguage" Title="{x:Static lang:Resources.SettingsView_DisplayTitle}"
                                Summary="{x:Static lang:Resources.SettingsView_DisplaySubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_TitleBarVisibilityLabelText}">
                <ToggleSwitch IsChecked="{Binding TitleBarVisibility,Mode=TwoWay}"
                              OnContent="{x:Static lang:Resources.Enum_On}"
                              OffContent="{x:Static lang:Resources.Enum_Off}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_SidebarPlacementLabelText}">
                <TabStrip SelectedIndex="{Binding SidebarPlacement,Mode=TwoWay}">
                    <TabStripItem>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <icons:PackIconLucide Kind="PanelLeft" Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" VerticalAlignment="Center" />
                            <husk:Divider Orientation="Vertical" />
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_SidebarPlacementLeftText}"
                                       VerticalAlignment="Center" />
                        </StackPanel>
                    </TabStripItem>
                    <TabStripItem>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_SidebarPlacementRightText}"
                                       VerticalAlignment="Center" />
                            <husk:Divider Orientation="Vertical" />
                            <icons:PackIconLucide Kind="PanelRight" Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" VerticalAlignment="Center" />
                        </StackPanel>
                    </TabStripItem>
                </TabStrip>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_ThemeVariantLabelText}"
                                        HorizontalContentAlignment="Left">
                <TabStrip Theme="{StaticResource SegmentedTabStripTheme}"
                          SelectedIndex="{Binding DarkMode,Mode=TwoWay}" AutoScrollToSelectedItem="False">
                    <TabStripItem>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <icons:PackIconLucide Kind="SunMoon" VerticalAlignment="Center"
                                                  Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" />
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_ThemeVariantSystemText}" />
                        </StackPanel>
                    </TabStripItem>
                    <TabStripItem>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <icons:PackIconLucide Kind="Sun" VerticalAlignment="Center"
                                                  Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" />
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_ThemeVariantLightText}" />
                        </StackPanel>
                    </TabStripItem>
                    <TabStripItem>
                        <StackPanel Orientation="Horizontal" Spacing="4">
                            <icons:PackIconLucide Kind="Moon" VerticalAlignment="Center"
                                                  Height="{StaticResource MediumFontSize}"
                                                  Width="{StaticResource MediumFontSize}" />
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_ThemeVariantDarkText}" />
                        </StackPanel>
                    </TabStripItem>
                </TabStrip>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_AccentColorLabelText}">
                <ComboBox ItemsSource="{Binding AccentColors}" SelectedIndex="{Binding AccentColor,Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate DataType="husk:AccentColor">
                            <TextBlock Text="{Binding}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_CornerStyleLabelText}"
                                        IsVisible="False">
                <ComboBox ItemsSource="{Binding CornerStyles}" SelectedIndex="{Binding CornerStyle,Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate DataType="husk:CornerStyle">
                            <TextBlock Text="{Binding}" />
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_BackgroundStyleLabelText}">
                <ComboBox SelectedIndex="{Binding BackgroundMode,Mode=TwoWay}">
                    <ComboBoxItem Content="{x:Static lang:Resources.SettingsView_BackgroundStyleAutoText}" />
                    <ComboBoxItem Content="{x:Static lang:Resources.SettingsView_BackgroundStyleMicaText}"
                                  IsEnabled="{OnPlatform {x:False},Windows={x:True}}" />
                    <ComboBoxItem Content="{x:Static lang:Resources.SettingsView_BackgroundStyleAcrylicText}"
                                  IsEnabled="{OnPlatform {x:False},Windows={x:True}}" />
                    <ComboBoxItem Content="{x:Static lang:Resources.SettingsView_BackgroundStyleBlurText}"
                                  IsEnabled="{OnPlatform {x:False},macOS={x:True}}" />
                    <ComboBoxItem Content="{x:Static lang:Resources.SettingsView_BackgroundStyleNoneText}" />
                </ComboBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_LanguageLabelText}">
                <ComboBox ItemsSource="{Binding Languages}" SelectedItem="{Binding Language,Mode=TwoWay}">
                    <ComboBox.ItemTemplate>
                        <DataTemplate DataType="m:LanguageModel">
                            <Grid ColumnDefinitions="*,4,Auto">
                                <TextBlock Grid.Column="0" Text="{Binding Display}" VerticalAlignment="Center" />
                                <husk:Tag Grid.Column="2" Classes="Small" VerticalAlignment="Center">
                                    <TextBlock Text="{Binding Id}" />
                                </husk:Tag>
                            </Grid>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_FontLabelText}">
                <TextBox Watermark="(Builtin)" IsEnabled="False" />
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="DrinkCoffee" Title="{x:Static lang:Resources.SettingsView_JavaTitle}"
                                Summary="{x:Static lang:Resources.SettingsView_JavaSubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_Java8LabelText}">
                <cp:JavaHomeContainer Home="{Binding JavaHome8,Mode=TwoWay}" OverlayService="{Binding OverlayService}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_Java11LabelText}">
                <cp:JavaHomeContainer Home="{Binding JavaHome11,Mode=TwoWay}" OverlayService="{Binding OverlayService}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_Java17LabelText}">
                <cp:JavaHomeContainer Home="{Binding JavaHome17,Mode=TwoWay}" OverlayService="{Binding OverlayService}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_Java21LabelText}">
                <cp:JavaHomeContainer Home="{Binding JavaHome21,Mode=TwoWay}" OverlayService="{Binding OverlayService}" />
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="Joystick" Title="{x:Static lang:Resources.SettingsView_GameDefaultsTitle}"
                                Summary="{x:Static lang:Resources.SettingsView_GameDefaultsSubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_JavaMaxMemoryLabelText}">
                <TextBox Watermark="{x:Static lang:Resources.SettingsView_JavaMaxMemoryPlaceholder}"
                         Text="{Binding JavaMaxMemory,Mode=TwoWay}">
                    <TextBox.InnerRightContent>
                        <StackPanel Orientation="Horizontal">
                            <husk:Divider Orientation="Vertical" />
                            <TextBlock Text="{x:Static lang:Resources.SettingsView_JavaMaxMemoryUnitText}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       Margin="8,0" VerticalAlignment="Center" />
                        </StackPanel>
                    </TextBox.InnerRightContent>
                </TextBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem
                Header="{x:Static lang:Resources.SettingsView_JavaAdditionalArgumentsLabelText}">
                <TextBox Watermark="{x:Static lang:Resources.SettingsView_JavaAdditionalArgumentsPlaceholder}"
                         Text="{Binding JavaAdditionalArguments,Mode=TwoWay}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_WindowInitialSizeLabelText}">
                <Grid ColumnDefinitions="*,7,*">
                    <TextBox Grid.Column="0" Watermark="1270" Text="{Binding WindowInitialHeight,Mode=TwoWay}">
                        <TextBox.InnerLeftContent>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{x:Static lang:Resources.SettingsView_WindowWidthLabelText}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Margin="8,0" VerticalAlignment="Center" />
                                <husk:Divider Orientation="Vertical" />
                            </StackPanel>
                        </TextBox.InnerLeftContent>
                    </TextBox>
                    <TextBox Grid.Column="2" Watermark="720" Text="{Binding WindowInitialWidth,Mode=TwoWay}">
                        <TextBox.InnerLeftContent>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{x:Static lang:Resources.SettingsView_WindowHeightLabelText}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Margin="8,0" VerticalAlignment="Center" />
                                <husk:Divider Orientation="Vertical" />
                            </StackPanel>
                        </TextBox.InnerLeftContent>
                    </TextBox>
                </Grid>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="Wrench" Title="{x:Static lang:Resources.SettingsView_MaintenanceTitle}"
                                Summary="{x:Static lang:Resources.SettingsView_MaintenanceSubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_StorageManagementLabelText}">
                <Button Content="{x:Static lang:Resources.SettingsView_StorageManagementButtonText}"
                        HorizontalAlignment="Left" Command="{Binding NavigateCommand}"
                        CommandParameter="{x:Type v:MaintenanceStorageView}" Classes="Small" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.SettingsView_UpdatesLabelText}">
                <controls:Plaque>
                    <controls:Plaque.Header>
                        <husk:SwitchPresenter
                            Value="{Binding UpdateTarget,Converter={x:Static ObjectConverters.IsNotNull}}"
                            TargetType="x:Boolean">
                            <husk:SwitchCase Value="{x:True}">
                                <DockPanel HorizontalSpacing="{StaticResource PlaqueSpacing}">
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource OutlineButtonTheme}"
                                            Classes="Small"
                                            Command="{Binding CheckUpdatesCommand}">
                                        <fi:SymbolIcon Symbol="ArrowClockwise" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource SmallFontSize}" />
                                    </Button>
                                    <Button Classes="Small Primary" Command="{Binding ApplyUpdateCommand}"
                                            CommandParameter="{Binding UpdateTarget}">
                                        <TextBlock Text="{x:Static lang:Resources.SettingsView_UpdatesApplyButtonText}" />
                                    </Button>
                                </DockPanel>
                            </husk:SwitchCase>
                            <husk:SwitchCase Value="{x:False}">
                                <Button Theme="{StaticResource OutlineButtonTheme}" Classes="Small"
                                        Command="{Binding CheckUpdatesCommand}">
                                    <TextBlock Text="{x:Static lang:Resources.SettingsView_UpdatesCheckButtonText}" />
                                </Button>
                            </husk:SwitchCase>
                        </husk:SwitchPresenter>
                    </controls:Plaque.Header>
                    <Grid ColumnDefinitions="*,Auto" ColumnSpacing="6">
                        <StackPanel Grid.Column="0" Spacing="4">
                            <TextBlock Text="{x:Static app:Program.BRAND}"
                                       FontWeight="{StaticResource ControlStrongFontWeight}" />
                            <TextBlock
                                Text="{Binding Source={x:Static app:Program.VERSION},StringFormat={}v{0},FallbackValue=v0.1.0}"
                                FontSize="{StaticResource LargeFontSize}" />
                            <StackPanel Orientation="Horizontal" Spacing="4">
                                <husk:SwitchPresenter Value="{x:Static app:Program.Debug}" TargetType="x:Boolean">
                                    <husk:SwitchCase Value="{x:True}">
                                        <TextBlock Text="In Dev"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{StaticResource SmallFontSize}" />
                                    </husk:SwitchCase>
                                    <husk:SwitchCase Value="{x:False}">
                                        <TextBlock Text="Prod"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{StaticResource SmallFontSize}" />
                                    </husk:SwitchCase>
                                </husk:SwitchPresenter>
                                <husk:Divider Orientation="Vertical" Margin="2" />
                                <TextBlock Text="Eternal"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           FontSize="{StaticResource SmallFontSize}" />
                            </StackPanel>
                        </StackPanel>
                        <DockPanel Grid.Column="1" VerticalSpacing="4" TextBlock.TextAlignment="Right">
                            <husk:SwitchPresenter DockPanel.Dock="Top" Value="{Binding UpdateState}"
                                                  TargetType="m:AppUpdateState">
                                <husk:SwitchCase Value="Idle" />
                                <husk:SwitchCase Value="Unavailable">
                                    <husk:Tag Classes="Small" VerticalAlignment="Top"
                                              HorizontalAlignment="Center">
                                        <husk:IconLabel Icon="DismissCircle" Variant="Filled" Text="{x:Static lang:Resources.SettingsView_UpdatesUnavailableLabelText}" />
                                    </husk:Tag>
                                </husk:SwitchCase>
                                <husk:SwitchCase Value="Latest">
                                    <husk:Tag Classes="Small Success" VerticalAlignment="Top"
                                              HorizontalAlignment="Center">
                                        <husk:IconLabel Icon="CheckmarkCircle" Variant="Filled" Text="{x:Static lang:Resources.SettingsView_UpdatesNotFoundLabelText}" />
                                    </husk:Tag>
                                </husk:SwitchCase>
                                <husk:SwitchCase Value="Found">
                                    <husk:Tag Classes="Small Primary" VerticalAlignment="Top"
                                              HorizontalAlignment="Center">
                                        <husk:IconLabel Icon="SparkleCircle" Variant="Filled" Text="{x:Static lang:Resources.SettingsView_UpdatesFoundLabelText}" />
                                    </husk:Tag>
                                </husk:SwitchCase>
                            </husk:SwitchPresenter>
                            <husk:PlaceholderPresenter Source="{Binding UpdateTarget}">
                                <husk:PlaceholderPresenter.SourceTemplate>
                                    <DataTemplate DataType="m:AppUpdateModel">
                                        <StackPanel Spacing="4">
                                            <TextBlock
                                                Text="{Binding Version,StringFormat={}v{0},FallbackValue=v0.2.0}"
                                                FontSize="{StaticResource LargeFontSize}" />
                                            <StackPanel Orientation="Horizontal" Spacing="4"
                                                        HorizontalAlignment="Right">
                                                <husk:SwitchPresenter Value="{Binding IsPrerelease}"
                                                                      TargetType="x:Boolean">
                                                    <husk:SwitchCase Value="{x:True}">
                                                        <TextBlock Text="Pre-release"
                                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                   FontSize="{StaticResource SmallFontSize}" />
                                                    </husk:SwitchCase>
                                                    <husk:SwitchCase Value="{x:False}">
                                                        <TextBlock Text="Release"
                                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                   FontSize="{StaticResource SmallFontSize}" />
                                                    </husk:SwitchCase>
                                                </husk:SwitchPresenter>
                                                <husk:Divider Orientation="Vertical" Margin="2" />
                                                <TextBlock Text="Unknown Date"
                                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                           FontSize="{StaticResource SmallFontSize}" />
                                            </StackPanel>
                                        </StackPanel>
                                    </DataTemplate>
                                </husk:PlaceholderPresenter.SourceTemplate>
                            </husk:PlaceholderPresenter>
                        </DockPanel>
                    </Grid>
                </controls:Plaque>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <Panel>
            <husk:ConstrainedBox HorizontalAlignment="Right" AspectRatio="0.6840277777777778"
                                 Height="{Binding #LinksPanel.Bounds.Height}">
                <Border CornerRadius="{StaticResource MediumCornerRadius}"
                        RenderOptions.BitmapInterpolationMode="HighQuality"
                        Background="{DynamicResource SettingsImageBrush}" />
            </husk:ConstrainedBox>
            <StackPanel Name="LinksPanel" Spacing="2">
                <TextBlock ClipToBounds="False">
                    <Run Text="Source Code:" />
                    <HyperlinkButton Content="GitHub" NavigateUri="https://github.com/d3ara1n/Polymerium" />
                    <Run Text="License:" />
                    <HyperlinkButton Content="MIT"
                                     NavigateUri="https://github.com/d3ara1n/Polymerium/blob/master/LICENSE.txt" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Image in the Settings Page:" />
                    <HyperlinkButton Content="Pixiv#126997360" NavigateUri="https://www.pixiv.net/artworks/126997360" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Architecture &amp; Engine:" />
                    <HyperlinkButton Content="Trident" NavigateUri="https://github.com/TridentCore/trident" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Platform Services:" />
                    <HyperlinkButton Content="PrismLauncher Meta" NavigateUri="https://meta.prismlauncher.org/" />
                    <HyperlinkButton Content="Starlight API" NavigateUri="https://docs.lunareclipse.studio/" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Repositories:" />
                    <HyperlinkButton Content="CurseForge" NavigateUri="https://docs.curseforge.com/" />
                    <HyperlinkButton Content="Modrinth" NavigateUri="https://docs.modrinth.com/" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Frontend Framework:" />
                    <HyperlinkButton Content="Avalonia" NavigateUri="https://avaloniaui.net" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Control Library:" />
                    <HyperlinkButton Content="Huskui" NavigateUri="https://github.com/d3ara1n/Huskui.Avalonia" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Color Palette:" />
                    <HyperlinkButton Content="Radix Colors" NavigateUri="https://www.radix-ui.com/colors" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Emoji:" />
                    <HyperlinkButton Content="Fluent Emoji" NavigateUri="https://github.com/microsoft/fluentui-emoji" />
                </TextBlock>
                <TextBlock ClipToBounds="False">
                    <Run Text="Icon Libraries:" />
                    <HyperlinkButton Content="Fluent Icons" NavigateUri="fluent2.microsoft.design/" />
                    <Run Text="(" />
                    <HyperlinkButton Content="FluentIcons.Avalonia"
                                     NavigateUri="https://github.com/davidxuang/FluentIcons" />
                    <Run Text=")" />
                    <Run Text="&amp;" />
                    <HyperlinkButton Content="Lucide Icons" NavigateUri="https://lucide.dev" />
                    <Run Text="(" />
                    <HyperlinkButton Content="IconPacks.Avalonia.Lucide"
                                     NavigateUri="https://github.com/MahApps/IconPacks.Avalonia" />
                    <Run Text=")" />
                </TextBlock>
            </StackPanel>
        </Panel>
        <husk:Divider />
        <StackPanel>
            <TextBlock Text="Disclaimer:" />
            <TextBlock Text="1. 作为 Advanced Instance Manager，Polymerium 仅提供功能，不提供任何服务，所使用的服务均来自第三方平台，日后也不会提供联机服务。"
                       TextWrapping="Wrap" />
            <TextBlock
                Text="2. Polymerium 为整合包创作者和整合包体验者设计，专门为游玩和编辑实例优化功能，为此引入了大量区别于传统的实例构建机制，这些机制可能会有极高的上手门槛，甚至会降低非目标用户的使用体验。"
                TextWrapping="Wrap" />
        </StackPanel>
        <StackPanel Orientation="Horizontal" Spacing="12">
            <Border Background="{DynamicResource SettingsAiBadgeEnglishImageBrush}"
                    Width="132"
                    Height="42" />
            <Border Background="{DynamicResource SettingsAiBadgeChineseImageBrush}"
                    Width="132"
                    Height="42" />
        </StackPanel>
    </StackPanel>
</husk:Page>

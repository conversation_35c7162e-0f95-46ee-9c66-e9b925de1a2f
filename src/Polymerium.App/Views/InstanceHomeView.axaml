<controls:Subpage
    x:Class="Polymerium.App.Views.InstanceHomeView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:Polymerium.App.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:fi="using:FluentIcons.Avalonia"
    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:v="using:Polymerium.App.Views"
    xmlns:vm="using:Polymerium.App.ViewModels"
    xmlns:trident="using:Trident.Core"
    xmlns:assets="clr-namespace:Polymerium.App.Assets"
    xmlns:async="clr-namespace:AsyncImageLoader;assembly=AsyncImageLoader.Avalonia"
    xmlns:m="clr-namespace:Polymerium.App.Models"
    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
    xmlns:converters="clr-namespace:Polymerium.App.Converters"
    xmlns:widgets="clr-namespace:Polymerium.App.Widgets"
    d:DesignHeight="566"
    d:DesignWidth="780"
    x:DataType="vm:InstanceHomeViewModel"
    mc:Ignorable="d">
    <controls:Subpage.Resources>
        <ControlTheme
            x:Key="SectionHeaderButtonTheme"
            BasedOn="{StaticResource GhostButtonTheme}"
            TargetType="Button">
            <Setter Property="HorizontalContentAlignment" Value="Stretch" />
            <Setter Property="Padding" Value="12,4" />
        </ControlTheme>
        <ControlTheme
            x:Key="LaunchButtonTheme"
            BasedOn="{StaticResource {x:Type Button}}"
            TargetType="Button">
            <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}" />
            <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
            <Setter Property="BorderThickness" Value="0,0,0,3" />
            <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
            <Setter Property="BorderBrush" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
            <Setter Property="RenderTransformOrigin" Value="50%,100%" />
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform"
                                                   Duration="{StaticResource ControlFastestAnimationDuration}" />
                    <ThicknessTransition Property="BorderThickness"
                                         Duration="{StaticResource ControlFastestAnimationDuration}" />
                </Transitions>
            </Setter>

            <Style Selector="^:pressed">
                <Setter Property="BorderThickness" Value="0" />
                <Setter Property="RenderTransform" Value="scaleY(0.98)" />
            </Style>
        </ControlTheme>
    </controls:Subpage.Resources>
    <Grid MaxWidth="1024" MaxHeight="750" RowDefinitions="*,*,100" ColumnSpacing="24" RowSpacing="24">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="230" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="*" MinWidth="230" />
        </Grid.ColumnDefinitions>
        <husk:Card
            Grid.Row="0"
            Grid.Column="0"
            Grid.ColumnSpan="2"
            Padding="0"
            Background="{StaticResource OverlaySolidBackgroundBrush}"
            BorderThickness="0">
            <Panel>
                <Border CornerRadius="{StaticResource MediumCornerRadius}">
                    <Border.Background>
                        <ImageBrush Source="{Binding Screenshot}" Stretch="UniformToFill" />
                    </Border.Background>
                    <Border.OpacityMask>
                        <LinearGradientBrush StartPoint="0%,10%" EndPoint="100%,100%">
                            <GradientStop Offset="0" Color="#3F000000" />
                            <GradientStop Offset="0.5" Color="#6F000000" />
                            <GradientStop Offset="1" Color="#DF000000" />
                        </LinearGradientBrush>
                    </Border.OpacityMask>
                </Border>
                <Grid Margin="24" RowDefinitions="Auto,8,Auto">
                    <husk:Tag Grid.Row="0" Content="{x:Static lang:Resources.InstanceHomeView_OverviewTitle}" />
                    <TextBlock
                        Grid.Row="2"
                        FontSize="36"
                        MaxLines="2"
                        Text="{Binding Basic.Name}"
                        TextTrimming="CharacterEllipsis"
                        TextWrapping="Wrap" />
                </Grid>
            </Panel>
        </husk:Card>
        <husk:BusyContainer
            Grid.Row="0"
            Grid.Column="2"
            CornerRadius="{StaticResource MediumCornerRadius}"
            IsBusy="{Binding State, Converter={x:Static husk:ObjectConverters.Match}, ConverterParameter=Updating}">
            <husk:Card
                BorderBrush="{StaticResource ControlBorderBrush}"
                BorderThickness="1">
                <Grid RowDefinitions="Auto,*,Auto" RowSpacing="8">
                    <StackPanel
                        Grid.Row="1"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Orientation="Horizontal">
                        <TextBlock FontSize="{StaticResource ExtraLargeFontSize}"
                                   Text="{Binding PackageCount, FallbackValue=114514}" />
                        <TextBlock
                            Margin="4"
                            VerticalAlignment="Bottom"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                            Text="{x:Static lang:Resources.InstanceHomeView_PackageCountText}" />
                    </StackPanel>
                    <Grid Grid.Row="0" ColumnDefinitions="Auto,8,*,8,Auto">
                        <icons:PackIconLucide
                            Grid.Column="0"
                            Height="{StaticResource LargeFontSize}"
                            Foreground="{StaticResource ControlAccentForegroundBrush}"
                            Kind="Boxes" />
                        <TextBlock
                            Grid.Column="2"
                            FontWeight="{StaticResource ControlStrongFontWeight}"
                            Text="{x:Static lang:Resources.InstanceHomeView_SetupTitle}" />
                        <Button
                            Grid.Column="4"
                            Command="{Binding $parent[v:InstanceView].NavigateCommand}"
                            CommandParameter="{x:Type v:InstanceSetupView}"
                            Theme="{StaticResource SectionHeaderButtonTheme}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <TextBlock FontSize="{StaticResource SmallFontSize}"
                                           Text="{x:Static lang:Resources.InstanceHomeView_SetupEditButtonText}" />
                                <fi:SymbolIcon FontSize="{StaticResource SmallFontSize}" Symbol="ArrowRight" />
                            </StackPanel>
                        </Button>
                    </Grid>
                    <husk:Card Grid.Row="2" Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                               BorderThickness="0" CornerRadius="{StaticResource MediumCornerRadius}">
                        <Grid ColumnDefinitions="*,Auto,*" ColumnSpacing="12">
                            <Grid Grid.Column="0" RowDefinitions="Auto,*" RowSpacing="4">
                                <TextBlock Grid.Row="0" Text="{x:Static lang:Resources.InstanceHomeView_SetupTypeText}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                <TextBlock Grid.Row="1"
                                           Text="{Binding Basic.LoaderLabel, FallbackValue=Type}"
                                           FontSize="{StaticResource LargeFontSize}" HorizontalAlignment="Center"
                                           VerticalAlignment="Center" />
                            </Grid>
                            <husk:Divider Grid.Column="1" Orientation="Vertical" />
                            <Grid Grid.Column="2" RowDefinitions="Auto,*" RowSpacing="4">
                                <TextBlock Grid.Row="0"
                                           Text="{x:Static lang:Resources.InstanceHomeView_SetupVersionText}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                <TextBlock Grid.Row="1"
                                           Text="{Binding Basic.Version, FallbackValue=Version}"
                                           FontSize="{StaticResource LargeFontSize}" HorizontalAlignment="Center"
                                           VerticalAlignment="Center" TextTrimming="CharacterEllipsis"
                                           ToolTip.Tip="{Binding Basic.Version,FallbackValue=Version}" />
                            </Grid>
                        </Grid>
                    </husk:Card>
                </Grid>
            </husk:Card>
            <husk:BusyContainer.PendingContent>
                <husk:ProgressRing
                    Width="56"
                    Height="56"
                    IsIndeterminate="True" HorizontalAlignment="Center" VerticalAlignment="Center" />
            </husk:BusyContainer.PendingContent>
        </husk:BusyContainer>
        <Grid
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.Column="0"
            RowDefinitions="Auto,12,*">
            <Grid Grid.Row="0" ColumnDefinitions="*,12,Auto">
                <TextBlock
                    Grid.Column="0"
                    FontSize="{StaticResource LargeFontSize}"
                    FontWeight="{StaticResource ControlStrongFontWeight}"
                    Text="{x:Static lang:Resources.InstanceHomeView_ActivitiesTitle}" />
                <Button
                    Grid.Row="0"
                    Grid.Column="2"
                    Command="{Binding $parent[v:InstanceView].NavigateCommand}"
                    CommandParameter="{x:Type v:InstanceActivitiesView}"
                    Theme="{StaticResource SectionHeaderButtonTheme}">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <TextBlock FontSize="{StaticResource SmallFontSize}"
                                   Text="{x:Static lang:Resources.InstanceHomeView_SeeMoreButtonText}" />
                        <fi:SymbolIcon FontSize="{StaticResource SmallFontSize}" Symbol="ArrowRight" />
                    </StackPanel>
                </Button>
            </Grid>
            <husk:Card Grid.Row="2">
                <Grid RowDefinitions="*,12,Auto">
                    <Panel
                        Grid.Row="0"
                        Width="122"
                        Height="122">
                        <husk:ProgressRing ShowProgressText="False"
                                           Value="{Binding PercentageInTotalPlayTime,FallbackValue=0.37}"
                                           Maximum="1"
                                           TrackStrokeWidth="2" TrackPadding="2" StrokeWidth="6" />
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock HorizontalAlignment="Center"
                                       Text="{x:Static lang:Resources.InstanceHomeView_HourCountText}" />
                            <TextBlock HorizontalAlignment="Center" FontSize="{StaticResource ExtraLargeFontSize}"
                                       Text="{Binding TotalPlayTime,StringFormat={}{0:0.0},FallbackValue=12.4}" />
                        </StackPanel>
                    </Panel>
                    <StackPanel Grid.Row="2" Spacing="8">
                        <husk:Card Background="Transparent">
                            <Grid ColumnDefinitions="Auto,4,*,4,Auto">
                                <fi:SymbolIcon
                                    Grid.Column="0"
                                    FontSize="{StaticResource MediumFontSize}"
                                    Symbol="HourglassHalf" />
                                <TextBlock Grid.Column="4" Text="{Binding LastPlayTime,FallbackValue=52 mins}" />
                            </Grid>
                        </husk:Card>
                        <husk:Card Background="Transparent">
                            <Grid ColumnDefinitions="Auto,4,*,4,Auto">
                                <fi:SymbolIcon
                                    Grid.Column="0"
                                    FontSize="{StaticResource MediumFontSize}"
                                    Symbol="Clock" />
                                <TextBlock Grid.Column="4" Text="{Binding LastPlayedAt,FallbackValue=one day ago}" />
                            </Grid>
                        </husk:Card>
                    </StackPanel>
                </Grid>
            </husk:Card>
        </Grid>
        <Grid
            Grid.Row="1"
            Grid.Column="1"
            Grid.ColumnSpan="2"
            RowDefinitions="Auto,12,*">
            <Grid Grid.Row="0" ColumnDefinitions="*,12,Auto">
                <TextBlock
                    Grid.Column="0"
                    FontSize="{StaticResource LargeFontSize}"
                    FontWeight="{StaticResource ControlStrongFontWeight}"
                    Text="{x:Static lang:Resources.InstanceHomeView_WidgetsTitle}" />
                <Button
                    Grid.Row="0"
                    Grid.Column="2"
                    Command="{Binding $parent[v:InstanceView].NavigateCommand}"
                    CommandParameter="{x:Type v:InstanceWidgetsView}"
                    Theme="{StaticResource SectionHeaderButtonTheme}">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <TextBlock FontSize="{StaticResource SmallFontSize}"
                                   Text="{x:Static lang:Resources.InstanceHomeView_SeeMoreButtonText}" />
                        <fi:SymbolIcon FontSize="{StaticResource SmallFontSize}" Symbol="ArrowRight" />
                    </StackPanel>
                </Button>
            </Grid>
            <Panel
                Grid.Row="2">
                <StackPanel VerticalAlignment="Center"
                            Spacing="8"
                            IsVisible="{Binding PinnedWidgets.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}">
                    <icons:PackIconLucide Kind="Microchip" Height="{StaticResource ExtraLargeFontSize}"
                                          Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                          Width="{StaticResource ExtraLargeFontSize}"
                                          HorizontalAlignment="Center" />
                    <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                               FontSize="{StaticResource LargeFontSize}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                               HorizontalAlignment="Center" />
                </StackPanel>
                <ScrollViewer
                    HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                    <ItemsControl
                        ItemsSource="{Binding PinnedWidgets,Mode=OneTime}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="widgets:WidgetBase">
                                <husk:Card>
                                    <ContentControl Content="{Binding}" ContentTemplate="{Binding SlimTemplate}" />
                                </husk:Card>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                        <ItemsControl.ItemContainerTheme>
                            <ControlTheme TargetType="ContentPresenter">
                                <Setter Property="Width" Value="198" />
                            </ControlTheme>
                        </ItemsControl.ItemContainerTheme>
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Orientation="Horizontal" Spacing="12" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                    </ItemsControl>
                </ScrollViewer>
            </Panel>
        </Grid>
        <Grid
            Grid.Row="2"
            Grid.Column="1"
            Grid.ColumnSpan="2"
            ColumnDefinitions="*,Auto" ColumnSpacing="8"
            RowDefinitions="Auto,*" RowSpacing="12">
            <TextBlock
                Grid.Row="0"
                FontSize="{StaticResource LargeFontSize}"
                FontWeight="{StaticResource ControlStrongFontWeight}"
                Text="{x:Static lang:Resources.InstanceHomeView_LaunchPadTitle}" />
            <husk:Card
                Grid.Row="1"
                Grid.Column="0"
                Padding="0">
                <husk:SwitchPresenter TargetType="trident:InstanceState" Value="{Binding State}">
                    <husk:SwitchCase Value="Idle">
                        <husk:SwitchPresenter
                            Value="{Binding SelectedAccount,Converter={x:Static ObjectConverters.IsNotNull},Mode=OneWay,FallbackValue={x:False}}"
                            TargetType="x:Boolean">
                            <husk:SwitchCase Value="{x:True}">
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal"
                                    Spacing="8">
                                    <fi:SymbolIcon
                                        VerticalAlignment="Center"
                                        FontSize="{StaticResource MediumFontSize}"
                                        Foreground="{StaticResource ControlSuccessForegroundBrush}"
                                        IconVariant="Filled"
                                        Symbol="CheckmarkCircle" />
                                    <TextBlock Foreground="{StaticResource ControlSuccessForegroundBrush}"
                                               Text="{x:Static lang:Resources.InstanceHomeView_ReadyLabelText}" />
                                </StackPanel>
                            </husk:SwitchCase>
                            <husk:SwitchCase Value="{x:False}">
                                <StackPanel
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal"
                                    Spacing="8">
                                    <fi:SymbolIcon
                                        VerticalAlignment="Center"
                                        FontSize="{StaticResource MediumFontSize}"
                                        Foreground="{StaticResource ControlDangerForegroundBrush}"
                                        IconVariant="Filled"
                                        Symbol="DismissCircle" />
                                    <TextBlock Foreground="{StaticResource ControlDangerForegroundBrush}"
                                               Text="{x:Static lang:Resources.InstanceHomeView_NoAccountLabelText}" />
                                </StackPanel>
                            </husk:SwitchCase>
                        </husk:SwitchPresenter>
                    </husk:SwitchCase>
                    <husk:SwitchCase Value="Deploying">
                        <Panel Margin="8">
                            <ProgressBar
                                CornerRadius="{StaticResource MediumCornerRadius}"
                                IsIndeterminate="{Binding DeployingPending, Mode=OneWay}"
                                Value="{Binding DeployingProgressCurrent, Mode=OneWay}"
                                Maximum="{Binding DeployingProgressTotal, Mode=OneWay}">
                                <ProgressBar.Transitions>
                                    <Transitions>
                                        <DoubleTransition
                                            Easing="SineEaseOut"
                                            Property="Value"
                                            Duration="{StaticResource ControlNormalAnimationDuration}" />
                                    </Transitions>
                                </ProgressBar.Transitions>
                            </ProgressBar>
                            <StackPanel
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center">
                                <TextBlock TextAlignment="Center"
                                           Text="{Binding DeployingMessage, Mode=OneWay}" />
                                <TextBlock TextAlignment="Center"
                                           IsVisible="{Binding DeployingPending, Converter={x:Static BoolConverters.Not}, Mode=OneWay}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           FontSize="{StaticResource SmallFontSize}">
                                    <Run Text="{Binding DeployingProgressCurrent,Mode=OneWay}" />
                                    <Run Text="/" />
                                    <Run Text="{Binding DeployingProgressTotal,Mode=OneWay}" />
                                </TextBlock>
                            </StackPanel>

                        </Panel>
                    </husk:SwitchCase>
                    <husk:SwitchCase Value="Running">
                        <TextBlock Loaded="Timer_OnLoaded" Unloaded="Timer_OnUnloaded"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   FontSize="{StaticResource ExtraLargeFontSize}"
                                   Text="{Binding TimerCount,StringFormat={}{0:hh\\:mm\\:ss}, Mode=OneWay,FallbackValue=0:0:24}" />
                    </husk:SwitchCase>
                </husk:SwitchPresenter>
            </husk:Card>
            <husk:Card
                Grid.Row="0"
                Grid.RowSpan="2"
                Grid.Column="1"
                Padding="0">
                <husk:SwitchPresenter TargetType="trident:InstanceState" Value="{Binding State}">
                    <husk:SwitchCase Value="Idle">
                        <Grid ColumnDefinitions="Auto,0,Auto">
                            <Button
                                Grid.Column="0"
                                Margin="8"
                                Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                                BorderBrush="{StaticResource ControlAccentTranslucentFullBackgroundBrush}"
                                Foreground="{StaticResource ControlBlackForegroundBrush}"
                                Command="{Binding PlayCommand}"
                                IsEnabled="{Binding State, Converter={x:Static husk:ObjectConverters.Match}, ConverterParameter=Idle}"
                                Theme="{StaticResource LaunchButtonTheme}">
                                <StackPanel VerticalAlignment="Center" Spacing="6">
                                    <fi:SymbolIcon
                                        HorizontalAlignment="Center"
                                        FontSize="20"
                                        Symbol="Rocket" />
                                    <TextBlock HorizontalAlignment="Center"
                                               Text="{x:Static lang:Resources.InstanceHomeView_LaunchButtonText}" />
                                </StackPanel>
                            </Button>
                            <Grid Grid.Column="2" RowDefinitions="Auto,0,*">
                                <Button Grid.Row="0"
                                        Command="{Binding SwitchModeCommand}"
                                        Margin="0,0,8,0"
                                        HorizontalAlignment="Center"
                                        CornerRadius="{Binding Source={StaticResource MediumCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Lower}}"
                                        Padding="12,6">
                                    <TextBlock
                                        Text="{Binding Mode,Converter={x:Static converters:InternalConverters.LocalizedLaunchModeConverter},FallbackValue=Mode}" />
                                </Button>
                                <Button
                                    Grid.Row="2"
                                    Margin="0,8,8,8"
                                    Padding="12,6"
                                    BorderBrush="{StaticResource ControlBorderBrush}"
                                    BorderThickness="1"
                                    Command="{Binding SwitchAccountCommand}"
                                    CornerRadius="{StaticResource MediumCornerRadius}"
                                    Theme="{StaticResource GhostButtonTheme}">
                                    <husk:PlaceholderPresenter Source="{Binding SelectedAccount}">
                                        <husk:PlaceholderPresenter.Placeholder>
                                            <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,*,Auto"
                                                  ColumnSpacing="8">
                                                <icons:PackIconLucide Grid.Row="0" Grid.Column="0" Grid.RowSpan="3"
                                                                      Width="28"
                                                                      Height="28" Kind="UserRound"
                                                                      VerticalAlignment="Center" />
                                                <TextBlock
                                                    Grid.Row="0"
                                                    Grid.Column="1"
                                                    FontSize="{StaticResource MediumFontSize}"
                                                    FontWeight="{StaticResource ControlStrongFontWeight}"
                                                    Text="{x:Static lang:Resources.InstanceHomeView_AccountButtonTitle}" />
                                                <TextBlock
                                                    Grid.Row="2"
                                                    Grid.Column="1"
                                                    FontSize="{StaticResource SmallFontSize}"
                                                    Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                    Text="{x:Static lang:Resources.InstanceHomeView_AccountButtonSubtitle}" />
                                            </Grid>
                                        </husk:PlaceholderPresenter.Placeholder>
                                        <husk:PlaceholderPresenter.SourceTemplate>
                                            <DataTemplate x:DataType="m:AccountModel">
                                                <Grid ColumnDefinitions="Auto,*" RowDefinitions="Auto,*,Auto"
                                                      ColumnSpacing="8">
                                                    <Border Grid.Row="0" Grid.Column="0" Grid.RowSpan="3"
                                                            Width="28"
                                                            Height="28"
                                                            CornerRadius="{StaticResource SmallCornerRadius}">
                                                        <Border.Background>
                                                            <ImageBrush
                                                                async:ImageBrushLoader.Source="{Binding FaceUrl,FallbackValue={x:Static assets:AssetUriIndex.STEVE_FACE_IMAGE}}" />
                                                        </Border.Background>
                                                    </Border>
                                                    <TextBlock
                                                        Grid.Row="0"
                                                        Grid.Column="1"
                                                        FontSize="{StaticResource MediumFontSize}"
                                                        FontWeight="{StaticResource ControlStrongFontWeight}"
                                                        Text="{Binding UserName,FallbackValue=Steve}" />
                                                    <TextBlock
                                                        Grid.Row="2"
                                                        Grid.Column="1"
                                                        FontSize="{StaticResource SmallFontSize}"
                                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                        Text="{Binding TypeName,FallbackValue=Legacy}" />
                                                </Grid>
                                            </DataTemplate>
                                        </husk:PlaceholderPresenter.SourceTemplate>
                                    </husk:PlaceholderPresenter>
                                </Button>
                            </Grid>
                        </Grid>
                    </husk:SwitchCase>
                    <husk:SwitchCase Value="Deploying">
                        <Button
                            Margin="8"
                            Command="{Binding AbortCommand}"
                            Theme="{StaticResource LaunchButtonTheme}">
                            <StackPanel VerticalAlignment="Center" Spacing="6">
                                <fi:SymbolIcon
                                    HorizontalAlignment="Center"
                                    FontSize="20"
                                    Symbol="Stop" />
                                <TextBlock HorizontalAlignment="Center"
                                           Text="{x:Static lang:Resources.InstanceHomeView_AbortButtonText}" />
                            </StackPanel>
                        </Button>
                    </husk:SwitchCase>
                    <husk:SwitchCase Value="Running">
                        <Grid ColumnDefinitions="Auto,0,Auto">
                            <Button
                                Grid.Column="0"
                                Margin="8"
                                Background="{StaticResource ControlDangerBackgroundBrush}"
                                BorderBrush="{StaticResource ControlDangerTranslucentFullBackgroundBrush}"
                                Command="{Binding StopCommand}"
                                Theme="{StaticResource LaunchButtonTheme}">
                                <StackPanel VerticalAlignment="Center" Spacing="6">
                                    <fi:SymbolIcon
                                        HorizontalAlignment="Center"
                                        FontSize="20"
                                        Symbol="Rocket" />
                                    <TextBlock HorizontalAlignment="Center"
                                               Text="{x:Static lang:Resources.InstanceHomeView_KillButtonText}" />
                                </StackPanel>
                            </Button>
                            <Grid Grid.Column="2" RowDefinitions="Auto,0,*">
                                <Button
                                    Grid.Row="0"
                                    Command="{Binding EjectCommand}"
                                    Margin="0,8,8,0"
                                    Classes="Small">
                                    <TextBlock Text="{x:Static lang:Resources.InstanceHomeView_DetachButtonText}" />
                                </Button>
                                <Button
                                    Grid.Row="2"
                                    Margin="0,8,8,8"
                                    Padding="12,6"
                                    BorderBrush="{StaticResource ControlBorderBrush}"
                                    BorderThickness="1"
                                    Command="{Binding OpenDashboardCommand}"
                                    CornerRadius="{StaticResource MediumCornerRadius}"
                                    Theme="{StaticResource GhostButtonTheme}">
                                    <Grid ColumnDefinitions="Auto,8,*" RowDefinitions="Auto,*,Auto">
                                        <icons:PackIconLucide
                                            Grid.Row="0"
                                            Grid.RowSpan="3"
                                            Grid.Column="0"
                                            Width="26"
                                            Height="26"
                                            VerticalAlignment="Center"
                                            Kind="Logs" />
                                        <TextBlock
                                            Grid.Row="0"
                                            Grid.Column="2"
                                            FontWeight="{StaticResource ControlStrongFontWeight}"
                                            Text="{x:Static lang:Resources.InstanceHomeView_DashboardButtonTitle}" />
                                        <TextBlock
                                            Grid.Row="2"
                                            Grid.Column="2"
                                            Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                            Text="{x:Static lang:Resources.InstanceHomeView_DashboardButtonSubtitle}" />
                                    </Grid>
                                </Button>
                            </Grid>
                        </Grid>
                    </husk:SwitchCase>
                </husk:SwitchPresenter>
            </husk:Card>
        </Grid>
    </Grid>
</controls:Subpage>

<husk:Dialog xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
             xmlns:local="clr-namespace:Polymerium.App.Dialogs"
             xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
             mc:Ignorable="d" Width="300" Title="{x:Static lang:Resources.ExportPackageListDialog_Title}"
             x:Class="Polymerium.App.Dialogs.PackageListExporterDialog"
             IsPrimaryButtonVisible="True"
             Message="{x:Static lang:Resources.ExportPackageListDialog_Prompt}">
    <StackPanel Spacing="12">
        <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}" Padding="3"
                CornerRadius="{StaticResource MediumCornerRadius}">
            <Border Background="{StaticResource OverlaySolidBackgroundBrush}"
                    CornerRadius="{StaticResource SmallCornerRadius}" Padding="12">
                <StackPanel Spacing="12" HorizontalAlignment="Center">
                    <TextBlock>
                        <Run
                            Text="{Binding $parent[local:PackageListExporterDialog].PackageCount,Mode=OneWay,FallbackValue=0}"
                            FontSize="{StaticResource ExtraLargeFontSize}" />
                        <Run Text="{x:Static lang:Resources.ExportPackageListDialog_PackageCountLabelText}"
                             Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </TextBlock>
                </StackPanel>
            </Border>
        </Border>
        <StackPanel Spacing="4">
            <TextBlock Text="{x:Static lang:Resources.ExportPackageListDialog_PathLabelText}" />
            <DockPanel HorizontalSpacing="8">
                <Button DockPanel.Dock="Right"
                        Command="{Binding $parent[local:PackageListExporterDialog].BrowseCommand}">
                    <fi:SymbolIcon Symbol="MoreHorizontal" HorizontalAlignment="Center"
                                   FontSize="{StaticResource MediumFontSize}" />
                </Button>
                <TextBox Watermark="{x:Static lang:Resources.ExportPackageListDialog_PathBarPlaceholder}"
                         Text="{Binding $parent[local:PackageListExporterDialog].Result,Mode=TwoWay}" />
            </DockPanel>
        </StackPanel>
    </StackPanel>
</husk:Dialog>

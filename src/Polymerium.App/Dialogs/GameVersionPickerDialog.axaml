<husk:Dialog xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
             xmlns:local="using:Polymerium.App.Dialogs"
             xmlns:m="using:Polymerium.App.Models"
             xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
             mc:Ignorable="d" Title="{x:Static lang:Resources.GameVersionPickerDialog_Title}"
             x:Class="Polymerium.App.Dialogs.GameVersionPickerDialog"
             IsPrimaryButtonVisible="True">
    <Grid RowDefinitions="Auto,12,Auto,12,*">
        <TextBox Grid.Row="0" Watermark="{x:Static lang:Resources.GameVersionPickerDialog_VersionBarPlaceholder}"
                 Text="{Binding $parent[local:GameVersionPickerDialog].FilterText,Mode=TwoWay}">
            <TextBox.InnerLeftContent>
                <StackPanel Orientation="Horizontal">
                    <icons:PackIconLucide Margin="10,0"
                                          Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                          Kind="ListFilter" Height="{StaticResource MediumFontSize}"
                                          VerticalAlignment="Center" />
                    <husk:Divider Orientation="Vertical" />
                </StackPanel>
            </TextBox.InnerLeftContent>
        </TextBox>
        <TabStrip Grid.Row="2" Theme="{StaticResource SegmentedTabStripTheme}" HorizontalAlignment="Center"
                  SelectedValue="{Binding $parent[local:GameVersionPickerDialog].SelectedType}"
                  ItemsSource="{Binding $parent[local:GameVersionPickerDialog].Types}">
            <TabStrip.ItemTemplate>
                <DataTemplate DataType="x:String">
                    <TextBlock Text="{Binding}" />
                </DataTemplate>
            </TabStrip.ItemTemplate>
        </TabStrip>
        <Panel Grid.Row="4" MinHeight="128">
            <StackPanel VerticalAlignment="Center"
                        IsVisible="{Binding $parent[local:GameVersionPickerDialog].View.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}"
                        Spacing="8">
                <icons:PackIconLucide Kind="Joystick" Height="{StaticResource ExtraLargeFontSize}"
                                      Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                      Width="{StaticResource ExtraLargeFontSize}" HorizontalAlignment="Center" />
                <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                           FontSize="{StaticResource LargeFontSize}"
                           Foreground="{StaticResource ControlSecondaryForegroundBrush}" HorizontalAlignment="Center" />
            </StackPanel>
            <ListBox MinHeight="64"
                     SelectedItem="{Binding $parent[local:GameVersionPickerDialog].Result,Mode=OneWayToSource}"
                     ItemsSource="{Binding $parent[local:GameVersionPickerDialog].View}">
                <ListBox.ItemTemplate>
                    <DataTemplate DataType="m:GameVersionModel">
                        <Grid ColumnDefinitions="*,4,Auto">
                            <TextBlock Grid.Column="0" Text="{Binding Name}" />
                            <husk:Tag Grid.Column="2" CornerRadius="{StaticResource SmallCornerRadius}"
                                      Content="{Binding ReleaseTime}" />
                        </Grid>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Panel>
    </Grid>
</husk:Dialog>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="using:System">

    <!-- Typography -->
    <FontWeight x:Key="ControlFontWeight">Normal</FontWeight>
    <FontWeight x:Key="ControlStrongFontWeight">Bold</FontWeight>

    <!-- Animations -->
    <sys:TimeSpan x:Key="ControlInstantAnimationDuration">0:0:0.088</sys:TimeSpan>
    <sys:TimeSpan x:Key="ControlFastestAnimationDuration">0:0:0.106</sys:TimeSpan>
    <sys:TimeSpan x:Key="ControlFasterAnimationDuration">0:0:0.197</sys:TimeSpan>
    <sys:TimeSpan x:Key="ControlNormalAnimationDuration">0:0:0.304</sys:TimeSpan>
    <sys:TimeSpan x:Key="ControlSlowerAnimationDuration">0:0:0.414</sys:TimeSpan>
    <sys:TimeSpan x:Key="ControlSlowestAnimationDuration">0:0:0.608</sys:TimeSpan>

    <!-- Float Layers Opacity -->
    <sys:Double x:Key="Overlay1Opacity">0.3</sys:Double>
    <sys:Double x:Key="Overlay2Opacity">0.5</sys:Double>
    <sys:Double x:Key="Overlay3Opacity">0.7</sys:Double>
    <sys:Double x:Key="OverlayFullOpacity">1.0</sys:Double>
    <sys:Double x:Key="ControlDimOpacity">0.5</sys:Double>

</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ResourceDictionary.MergedDictionaries>
        <ResourceInclude Source="/Controls/AdornerLayer.axaml" />
        <ResourceInclude Source="/Controls/AppWindow.axaml" />
        <ResourceInclude Source="/Controls/Button.axaml" />
        <ResourceInclude Source="/Controls/ButtonGroup.axaml" />
        <ResourceInclude Source="/Controls/BusyContainer.axaml" />
        <ResourceInclude Source="/Controls/Card.axaml" />
        <ResourceInclude Source="/Controls/Carousel.axaml" />
        <ResourceInclude Source="/Controls/PipsPager.axaml" />
        <ResourceInclude Source="/Controls/CheckBox.axaml" />
        <ResourceInclude Source="/Controls/ComboBox.axaml" />
        <ResourceInclude Source="/Controls/Dialog.axaml" />
        <ResourceInclude Source="/Controls/Divider.axaml" />
        <ResourceInclude Source="/Controls/DropDownButton.axaml" />
        <ResourceInclude Source="/Controls/DropZone.axaml" />
        <ResourceInclude Source="/Controls/Expander.axaml" />
        <ResourceInclude Source="/Controls/Flyout.axaml" />
        <ResourceInclude Source="/Controls/Frame.axaml" />
        <ResourceInclude Source="/Controls/HighlightBlock.axaml" />
        <ResourceInclude Source="/Controls/HyperlinkButton.axaml" />
        <ResourceInclude Source="/Controls/IconLabel.axaml" />
        <ResourceInclude Source="/Controls/InfiniteScrollView.axaml" />
        <ResourceInclude Source="/Controls/InfoBar.axaml" />
        <ResourceInclude Source="/Controls/ItemsControl.axaml" />
        <ResourceInclude Source="/Controls/LazyContainer.axaml" />
        <ResourceInclude Source="/Controls/ListBox.axaml" />
        <ResourceInclude Source="/Controls/Menu.axaml" />
        <ResourceInclude Source="/Controls/Modal.axaml" />
        <ResourceInclude Source="/Controls/NotificationHost.axaml" />
        <ResourceInclude Source="/Controls/NotificationItem.axaml" />
        <ResourceInclude Source="/Controls/OverlayHost.axaml" />
        <ResourceInclude Source="/Controls/OverlayItem.axaml" />
        <ResourceInclude Source="/Controls/Page.axaml" />
        <ResourceInclude Source="/Controls/PlaceholderPresenter.axaml" />
        <ResourceInclude Source="/Controls/Popup.axaml" />
        <ResourceInclude Source="/Controls/ProgressBar.axaml" />
        <ResourceInclude Source="/Controls/ProgressRing.axaml" />
        <ResourceInclude Source="/Controls/RadioButton.axaml" />
        <ResourceInclude Source="/Controls/ScrollBar.axaml" />
        <ResourceInclude Source="/Controls/ScrollViewer.axaml" />
        <ResourceInclude Source="/Controls/Separator.axaml" />
        <ResourceInclude Source="/Controls/SkeletonContainer.axaml" />
        <ResourceInclude Source="/Controls/Slider.axaml" />
        <ResourceInclude Source="/Controls/SplitButton.axaml" />
        <ResourceInclude Source="/Controls/StepControl.axaml" />
        <ResourceInclude Source="/Controls/StepItem.axaml" />
        <ResourceInclude Source="/Controls/SwitchPresenter.axaml" />
        <ResourceInclude Source="/Controls/TabControl.axaml" />
        <ResourceInclude Source="/Controls/TabStrip.axaml" />
        <ResourceInclude Source="/Controls/Tag.axaml" />
        <ResourceInclude Source="/Controls/TextBox.axaml" />
        <ResourceInclude Source="/Controls/TimelineControl.axaml" />
        <ResourceInclude Source="/Controls/TimelineItem.axaml" />
        <ResourceInclude Source="/Controls/TransitioningContentControl.axaml" />
        <ResourceInclude Source="/Controls/Toast.axaml" />
        <ResourceInclude Source="/Controls/ToggleButton.axaml" />
        <ResourceInclude Source="/Controls/ToggleSplitButton.axaml" />
        <ResourceInclude Source="/Controls/ToggleSwitch.axaml" />
        <ResourceInclude Source="/Controls/ToolTip.axaml" />
    </ResourceDictionary.MergedDictionaries>
</ResourceDictionary>

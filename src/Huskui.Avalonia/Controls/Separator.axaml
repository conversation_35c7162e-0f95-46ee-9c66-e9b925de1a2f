<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="{x:Type Separator}"
                  TargetType="Separator">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Background" Value="{StaticResource ControlBackgroundBrush}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="Height" Value="1" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

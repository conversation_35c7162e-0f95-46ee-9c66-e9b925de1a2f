<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Grid ColumnDefinitions="*,12,*" Background="White">
            <StackPanel Grid.Column="0" Width="128" Margin="32" VerticalAlignment="Center" Orientation="Horizontal"
                        Spacing="8">
                <local:ProgressRing Value="50" Height="54" Width="54"
                                    ShowProgressText="True">
                    <local:ProgressRing.Styles>
                        <Style Selector="local|ProgressRing">
                            <Style.Animations>
                                <Animation Duration="0:0:3" IterationCount="Infinite" Easing="QuadraticEaseOut">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="Value" Value="0" />
                                    </KeyFrame>
                                    <KeyFrame Cue="50%">
                                        <Setter Property="Value" Value="100" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="Value" Value="0" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </local:ProgressRing.Styles>
                </local:ProgressRing>
                <local:ProgressRing Width="54" Height="54" IsIndeterminate="True"
                                    ShowProgressText="True" />
            </StackPanel>
            <StackPanel Grid.Column="2" Height="128" Margin="32" Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <Panel Width="20" Height="20">
                    <local:ProgressRing Value="50" Theme="{StaticResource SolidProgressRingTheme}"
                                        ShowProgressText="True" IsIndeterminate="True" />
                </Panel>
            </StackPanel>
        </Grid>
    </Design.PreviewWith>
    <ControlTheme x:Key="BaseProgressRingTheme" TargetType="local:ProgressRing">
        <Setter Property="StrokeWidth" Value="2" />
        <Setter Property="TrackStrokeWidth" Value="2" />
        <Setter Property="TrackPadding" Value="0" />
        <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
        <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="ProgressTextFormat" Value="{}{1:0}%" />
        <Setter Property="ShowProgressText" Value="True" />

    </ControlTheme>

    <ControlTheme x:Key="{x:Type local:ProgressRing}" TargetType="local:ProgressRing"
                  BasedOn="{StaticResource BaseProgressRingTheme}">
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container">
                    <Arc Name="Track"
                         StartAngle="0"
                         Margin="{TemplateBinding TrackPadding}"
                         Stretch="None"
                         Stroke="{StaticResource ControlBackgroundBrush}"
                         StrokeThickness="{TemplateBinding TrackStrokeWidth}"
                         SweepAngle="360" />
                    <Panel Name="Indeterminate" IsVisible="False">
                        <Arc Name="Chaser"
                             Stretch="None"
                             SweepAngle="90"
                             Stroke="{TemplateBinding Background}"
                             StrokeThickness="{TemplateBinding StrokeWidth}"
                             StrokeLineCap="Round" />
                    </Panel>
                    <Panel Name="Determinate">
                        <Arc Name="{x:Static local:ProgressRing.PART_Indicator}"
                             Stretch="None"
                             StartAngle="-90"
                             SweepAngle="{TemplateBinding Angle}"
                             Stroke="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                             StrokeThickness="{TemplateBinding StrokeWidth}" StrokeLineCap="Round" />
                    </Panel>
                    <TextBlock Name="PercentageText" FontSize="{TemplateBinding FontSize}" HorizontalAlignment="Center"
                               VerticalAlignment="Center" IsVisible="{TemplateBinding ShowProgressText}">
                        <TextBlock.Text>
                            <MultiBinding Converter="{x:Static local:InternalConverters.StringFormat}">
                                <TemplateBinding Property="ProgressTextFormat" />
                                <Binding Path="Value"
                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                <TemplateBinding Property="Percentage" />
                                <TemplateBinding Property="Minimum" />
                                <TemplateBinding Property="Maximum" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^:indeterminate">
            <Style Selector="^ /template/ Panel#Determinate">
                <Setter Property="IsVisible" Value="False" />
            </Style>
            <Style Selector="^ /template/ Panel#Indeterminate">
                <Setter Property="IsVisible" Value="True" />
            </Style>
            <Style Selector="^ /template/ TextBlock#PercentageText">
                <Setter Property="IsVisible" Value="False" />
            </Style>

            <Style Selector="^ /template/ Arc#Chaser">
                <Style.Animations>
                    <Animation Duration="0:0:1.5" IterationCount="Infinite">
                        <KeyFrame Cue="0%">
                            <Setter Property="StartAngle"
                                    Value="-60" />
                        </KeyFrame>
                        <KeyFrame Cue="100%">
                            <Setter Property="StartAngle"
                                    Value="300" />
                        </KeyFrame>
                    </Animation>
                </Style.Animations>
            </Style>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="SolidProgressRingTheme" TargetType="local:ProgressRing"
                  BasedOn="{StaticResource BaseProgressRingTheme}">
        <Setter Property="Foreground" Value="{StaticResource ControlReversedForegroundBrush}" />
        <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="TrackStrokeWidth" Value="2" />
        <Setter Property="TrackPadding" Value="3" />
        <Setter Property="StrokeWidth" Value="2" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Container">
                    <Arc Name="Track"
                         StartAngle="0"
                         Stretch="None"
                         Stroke="{TemplateBinding Background}"
                         StrokeThickness="{TemplateBinding TrackStrokeWidth}"
                         SweepAngle="360" />
                    <Panel Name="Indeterminate" IsVisible="False"
                           Margin="{TemplateBinding TrackPadding}">
                        <Sector Name="Chaser"
                                Stretch="None"
                                StartAngle="-90"
                                Fill="{TemplateBinding Background}" />
                    </Panel>
                    <Panel Name="Determinate"
                           Margin="{TemplateBinding TrackPadding}">
                        <Sector Name="{x:Static local:ProgressRing.PART_Indicator}"
                                StartAngle="-90"
                                Stretch="None"
                                SweepAngle="{TemplateBinding Angle}"
                                Fill="{TemplateBinding Background}" />
                    </Panel>
                    <TextBlock Name="PercentageText" FontSize="{TemplateBinding FontSize}" HorizontalAlignment="Center"
                               VerticalAlignment="Center" IsVisible="{TemplateBinding ShowProgressText}"
                               Margin="{TemplateBinding TrackPadding}">
                        <TextBlock.Text>
                            <MultiBinding Converter="{x:Static local:InternalConverters.StringFormat}">
                                <TemplateBinding Property="ProgressTextFormat" />
                                <Binding Path="Value"
                                         RelativeSource="{RelativeSource TemplatedParent}" />
                                <TemplateBinding Property="Percentage" />
                                <TemplateBinding Property="Minimum" />
                                <TemplateBinding Property="Maximum" />
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Panel#Container">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
        <Style Selector="^:indeterminate">
            <Style Selector="^ /template/ Panel#Determinate">
                <Setter Property="IsVisible" Value="False" />
            </Style>
            <Style Selector="^ /template/ Panel#Indeterminate">
                <Setter Property="IsVisible" Value="True" />
            </Style>
            <Style Selector="^ /template/ TextBlock#PercentageText">
                <Setter Property="IsVisible" Value="False" />
            </Style>

            <Style Selector="^ /template/ Sector#Chaser">
                <Style.Animations>
                    <Animation Duration="0:0:3" IterationCount="Infinite">
                        <KeyFrame Cue="0%">
                            <Setter Property="SweepAngle"
                                    Value="0" />
                        </KeyFrame>
                        <KeyFrame Cue="50%">
                            <Setter Property="SweepAngle"
                                    Value="360" />
                        </KeyFrame>
                        <KeyFrame Cue="100%">
                            <Setter Property="SweepAngle"
                                    Value="0" />
                        </KeyFrame>
                    </Animation>
                    <!-- TODO: 迭代次数为无限会导致 DelayBetweenIterations 一次之后后续就不进行 Duration 了 -->
                    <Animation Duration="0:0:1.5" Delay="0:0:1.5" DelayBetweenIterations="0:0:1.5"
                               IterationCount="99999">
                        <KeyFrame Cue="0%">
                            <Setter Property="StartAngle"
                                    Value="-90" />
                        </KeyFrame>
                        <KeyFrame Cue="100%">
                            <Setter Property="StartAngle"
                                    Value="270" />
                        </KeyFrame>
                    </Animation>
                </Style.Animations>
            </Style>
        </Style>
    </ControlTheme>
</ResourceDictionary>

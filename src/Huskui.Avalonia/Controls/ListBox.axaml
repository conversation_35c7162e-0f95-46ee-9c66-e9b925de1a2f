<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Panel Background="White">
            <ListBox Margin="20" Width="220" Height="220" BorderThickness="1" BorderBrush="Black">
                <ListBoxItem>Item 1</ListBoxItem>
                <ListBoxItem>Item 2</ListBoxItem>
                <ListBoxItem>Item 3</ListBoxItem>
                <ListBoxItem IsEnabled="False" IsSelected="True"> Selected Disabled</ListBoxItem>
                <ListBoxItem IsEnabled="False" IsSelected="False">Disabled</ListBoxItem>
                <ListBoxItem>Item 4</ListBoxItem>
            </ListBox>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type ListBox}" TargetType="ListBox">
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.IsScrollChainingEnabled" Value="True" />
        <Setter Property="ItemsPanel">
            <ItemsPanelTemplate>
                <!-- Spacing Property -->
                <!-- https://github.com/AvaloniaUI/Avalonia/pull/17356 -->
                <VirtualizingStackPanel />
            </ItemsPanelTemplate>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border
                    Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    ClipToBounds="{TemplateBinding ClipToBounds}"
                    CornerRadius="{TemplateBinding CornerRadius}">
                    <ScrollViewer
                        Name="PART_ScrollViewer" ClipToBounds="{TemplateBinding ClipToBounds}"
                        AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                        HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                        IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                        IsDeferredScrollingEnabled="{TemplateBinding (ScrollViewer.IsDeferredScrollingEnabled)}"
                        IsScrollInertiaEnabled="{TemplateBinding (ScrollViewer.IsScrollInertiaEnabled)}"
                        VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                        <ItemsPresenter
                            Name="PART_ItemsPresenter"
                            Margin="{TemplateBinding Padding}"
                            ItemsPanel="{TemplateBinding ItemsPanel}" />
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type ListBoxItem}" TargetType="ListBoxItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="{StaticResource ControlTranslucentHalfBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Template">
            <ControlTemplate TargetType="ListBoxItem">
                <Panel>
                    <Border x:Name="Background" Margin="4,2,4,2"
                            BackgroundSizing="{TemplateBinding BackgroundSizing}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background" Easing="SineEaseInOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <BrushTransition Property="BorderBrush" Easing="SineEaseInOut"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <Border Name="Indicator" Width="3" Opacity="0" HorizontalAlignment="Left"
                            RenderTransform="translateX(-4px)" BorderThickness="1"
                            BorderBrush="{StaticResource ControlAccentTranslucentFullBackgroundBrush}"
                            Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                            CornerRadius="{StaticResource SmallCornerRadius}" Margin="0,4">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <ContentPresenter
                        Name="PART_ContentPresenter"
                        Padding="{TemplateBinding Padding}"
                        ClipToBounds="{TemplateBinding ClipToBounds}"
                        HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                        VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                        Content="{TemplateBinding Content}"
                        ContentTemplate="{TemplateBinding ContentTemplate}"
                        Foreground="{TemplateBinding Foreground}" />
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover:not(:selected) /template/ Border#Background">
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}" />
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>


        <Style Selector="^:selected">
            <Style Selector="^ /template/ Border#Background">
                <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
                <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            </Style>
            <Style Selector="^ /template/ Border#Indicator">
                <Setter Property="Opacity" Value="1" />
                <Setter Property="RenderTransform" Value="translateX(0px)" />
            </Style>
        </Style>

        <Style Selector="^:disabled">
            <Style Selector="^ /template/ Border#Background">
                <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
            </Style>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="CleanListBoxItemTheme" TargetType="ListBoxItem">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter
                    Name="PART_ContentPresenter"
                    Padding="{TemplateBinding Padding}"
                    Background="{TemplateBinding Background}"
                    BackgroundSizing="{TemplateBinding BackgroundSizing}"
                    ClipToBounds="{TemplateBinding ClipToBounds}"
                    CornerRadius="{TemplateBinding CornerRadius}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                    Content="{TemplateBinding Content}"
                    ContentTemplate="{TemplateBinding ContentTemplate}"
                    Foreground="{TemplateBinding Foreground}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="{x:Type ItemsControl}"
                  TargetType="ItemsControl">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <ItemsPresenter Name="PART_ItemsPresenter"
                                    ItemsPanel="{TemplateBinding ItemsPanel}" />
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

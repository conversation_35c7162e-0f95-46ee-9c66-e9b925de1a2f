<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="using:FluentIcons.Avalonia">
    <Thickness x:Key="ToastContentMargin">24,70,24,20</Thickness>
    <Thickness x:Key="ToastHeaderlessContentMargin">24</Thickness>
    <Thickness x:Key="ToastHeaderContentMargin">0,70,0,0</Thickness>
    <Thickness x:Key="ToastToplessContentMargin">24,0,24,24</Thickness>
    <ControlTheme x:Key="{x:Type local:Toast}" TargetType="local:Toast">
        <Setter Property="Background" Value="{StaticResource FlyoutBackgroundBrush}" />
        <Setter Property="Padding" Value="{StaticResource ToastContentMargin}" />
        <Setter Property="BackgroundSizing" Value="InnerBorderEdge" />
        <Setter Property="FontWeight" Value="{StaticResource ControlFontWeight}" />
        <Setter Property="CornerRadius"
                Value="{Binding Source={StaticResource LargeCornerRadius},Converter={x:Static local:CornerRadiusConverters.Upper}}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border CornerRadius="{TemplateBinding CornerRadius}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        ClipToBounds="{TemplateBinding ClipToBounds}"
                        BackgroundSizing="{TemplateBinding BackgroundSizing}" Background="{TemplateBinding Background}">
                    <Panel>
                        <ScrollViewer
                            Name="ScrollViewer"
                            HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                            VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                            <ContentPresenter Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              Padding="{TemplateBinding Padding}" />
                        </ScrollViewer>
                        <Border Name="HeaderBackground" VerticalAlignment="Top"
                                IsVisible="{TemplateBinding IsHeaderVisible}"
                                CornerRadius="{TemplateBinding CornerRadius,Converter={x:Static local:CornerRadiusConverters.Upper}}">
                            <DockPanel Margin="{StaticResource ToastHeaderlessContentMargin}">
                                <Button Padding="8" VerticalAlignment="Top"
                                        CornerRadius="{StaticResource FullCornerRadius}"
                                        Theme="{StaticResource OutlineButtonTheme}"
                                        DockPanel.Dock="Right" IsCancel="True"
                                        Command="{Binding Dismiss,RelativeSource={RelativeSource Mode=TemplatedParent}}">
                                    <fi:SymbolIcon Symbol="Dismiss" Height="{StaticResource LargeFontSize}"
                                                   Width="{StaticResource LargeFontSize}" />
                                </Button>
                                <ContentPresenter Content="{TemplateBinding Header}"
                                                  ContentTemplate="{TemplateBinding HeaderTemplate}"
                                                  IsVisible="{TemplateBinding IsHeaderVisible}">
                                    <ContentPresenter.Opacity>
                                        <MultiBinding Converter="{x:Static local:InternalConverters.OffsetToOpacity}">
                                            <Binding ElementName="ScrollViewer" Path="Offset" />
                                            <Binding ElementName="HeaderBackground" Path="Bounds.Height" />
                                        </MultiBinding>
                                    </ContentPresenter.Opacity>
                                    <ContentPresenter.Styles>
                                        <Style Selector="ContentPresenter > TextBlock">
                                            <Setter Property="FontSize" Value="{StaticResource ExtraLargeFontSize}" />
                                            <Setter Property="FontWeight"
                                                    Value="{StaticResource ControlStrongFontWeight}" />
                                            <Setter Property="VerticalAlignment" Value="Center" />
                                        </Style>
                                    </ContentPresenter.Styles>
                                </ContentPresenter>
                            </DockPanel>
                        </Border>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

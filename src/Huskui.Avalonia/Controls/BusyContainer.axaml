<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Height="328" Width="256">
            <Grid Margin="24" RowDefinitions="*,8,*,8,*">
                <local:BusyContainer Grid.Row="0">
                    <Button>Hello World</Button>
                </local:BusyContainer>
                <local:BusyContainer Grid.Row="2">
                    <Button>Hello World</Button>
                </local:BusyContainer>
                <local:BusyContainer Grid.Row="4">
                    <Button>Hello World</Button>
                </local:BusyContainer>
            </Grid>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:BusyContainer}" TargetType="local:BusyContainer">
        <Setter Property="Background" Value="{StaticResource OverlayMaskBackgroundBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border ClipToBounds="{TemplateBinding ClipToBounds}">
                    <Panel>
                        <ContentPresenter Name="PART_ContentPresenter" Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}"
                                          Padding="{TemplateBinding Padding}" />
                        <Border Name="Mask" Background="{TemplateBinding Background}"
                                IsVisible="False" BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <ContentPresenter Content="{TemplateBinding PendingContent}" />
                        </Border>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:busy /template/ Border#Mask">
            <Setter Property="IsVisible" Value="True" />
        </Style>
        <Style Selector="^:busy /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Effect">
                <BlurEffect />
            </Setter>
        </Style>
    </ControlTheme>
</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <Design.PreviewWith>
        <Grid ColumnDefinitions="*,32,*" Background="White">
            <StackPanel Grid.Column="0" Width="128" Margin="32" Spacing="36" VerticalAlignment="Center">
                <ProgressBar Value="50"
                             ShowProgressText="True">
                    <ProgressBar.Styles>
                        <Style Selector="ProgressBar">
                            <Style.Animations>
                                <Animation Duration="0:0:1" IterationCount="Infinite" Easing="QuadraticEaseOut">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="Value" Value="0" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="Value" Value="100" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </ProgressBar.Styles>
                </ProgressBar>
                <ProgressBar Value="50" IsIndeterminate="True"
                             ShowProgressText="True" />
            </StackPanel>
            <StackPanel Grid.Column="2" Height="128" Margin="32" Spacing="36" Orientation="Horizontal"
                        HorizontalAlignment="Center">
                <ProgressBar Value="50"
                             ShowProgressText="True" Orientation="Vertical">
                    <ProgressBar.Styles>
                        <Style Selector="ProgressBar">
                            <Style.Animations>
                                <Animation Duration="0:0:1" IterationCount="Infinite" Easing="QuadraticEaseOut">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="Value" Value="0" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="Value" Value="100" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </ProgressBar.Styles>
                </ProgressBar>
                <ProgressBar Value="50" IsIndeterminate="True" Orientation="Vertical"
                             ShowProgressText="True" />
            </StackPanel>
        </Grid>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type ProgressBar}" TargetType="ProgressBar">
        <Setter Property="Background" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="TextBlock.FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="ProgressTextFormat" Value="{}{1:0}%" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Border" ClipToBounds="True"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        Background="{StaticResource ControlBackgroundBrush}"
                        Padding="{TemplateBinding Padding}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Panel>
                        <!-- <Popup Name="PART_Popup" IsOpen="{TemplateBinding ShowProgressText}"> -->
                        <!--     <Border Background="{StaticResource CardBackgroundBrush}" -->
                        <!--             BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1" -->
                        <!--             CornerRadius="{StaticResource SmallCornerRadius}" Padding="4,0" Margin="6"> -->
                        <!--         <TextBlock -->
                        <!--             FontSize="{TemplateBinding FontSize}" HorizontalAlignment="Center"> -->
                        <!--             <TextBlock.Text> -->
                        <!--                 <MultiBinding Converter="{x:Static huskc:InternalConverters.StringFormat}"> -->
                        <!--                     <TemplateBinding Property="ProgressTextFormat" /> -->
                        <!--                     <Binding Path="Value" -->
                        <!--                              RelativeSource="{RelativeSource TemplatedParent}" /> -->
                        <!--                     <TemplateBinding Property="Percentage" /> -->
                        <!--                     <TemplateBinding Property="Minimum" /> -->
                        <!--                     <TemplateBinding Property="Maximum" /> -->
                        <!--                 </MultiBinding> -->
                        <!--             </TextBlock.Text> -->
                        <!--         </TextBlock> -->
                        <!--     </Border> -->
                        <!-- </Popup> -->
                        <Panel Name="Determinate">
                            <Border Name="PART_Indicator"
                                    Background="{TemplateBinding Background}" />
                        </Panel>
                        <Panel Name="Indeterminate" Opacity="0.0">
                            <Border Name="Runner"
                                    Background="{TemplateBinding Background}" Opacity="0.5" />
                            <Border Name="Chaser"
                                    Background="{TemplateBinding Background}" Opacity="1" />
                        </Panel>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Border#Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^:horizontal">
            <Setter Property="MinHeight" Value="4" />

            <!-- <Style Selector="^ /template/ Popup#PART_Popup"> -->
            <!--     <Setter Property="Placement" Value="Top" /> -->
            <!-- </Style> -->

            <Style Selector="^ /template/ Border#PART_Indicator">
                <Setter Property="HorizontalAlignment" Value="Left" />
            </Style>
            <Style Selector="^ /template/ Panel#Indeterminate">
                <Setter Property="RenderTransformOrigin" Value="100%,50%" />
            </Style>
            <Style Selector="^ /template/ Border#Runner">
                <Setter Property="RenderTransformOrigin" Value="0%,50%" />
            </Style>
            <Style Selector="^ /template/ Border#Chaser">
                <Setter Property="RenderTransformOrigin" Value="0%,50%" />
            </Style>

            <Style Selector="^:indeterminate">
                <!-- <Style Selector="^ /template/ Popup#PART_Popup"> -->
                <!--     <Setter Property="IsOpen" Value="False" /> -->
                <!-- </Style> -->

                <Style Selector="^ /template/ Panel#Determinate">
                    <Setter Property="Opacity" Value="0.0" />
                </Style>
                <Style Selector="^ /template/ Panel#Indeterminate">
                    <Setter Property="Opacity" Value="1.0" />
                </Style>

                <Style Selector="^ /template/ Border#Runner">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="33%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
                <Style Selector="^ /template/ Border#Chaser">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite" Easing="SineEaseOut">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="33%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="66%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
                <Style Selector="^ /template/ Panel#Indeterminate">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite" Easing="CubicEaseInOut">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="50%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleX"
                                        Value="0" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
            </Style>
        </Style>

        <Style Selector="^:vertical">
            <Setter Property="MinWidth" Value="4" />

            <!-- <Style Selector="^ /template/ Popup#PART_Popup"> -->
            <!--     <Setter Property="Placement" Value="Bottom" /> -->
            <!-- </Style> -->

            <Style Selector="^ /template/ Border#PART_Indicator">
                <Setter Property="VerticalAlignment" Value="Bottom" />
            </Style>
            <Style Selector="^ /template/ Panel#PART_Indeterminate">
                <Setter Property="RenderTransformOrigin" Value="50%,0%" />
            </Style>
            <Style Selector="^ /template/ Border#PART_Runner">
                <Setter Property="RenderTransformOrigin" Value="50%,100%" />
            </Style>
            <Style Selector="^ /template/ Border#PART_Chaser">
                <Setter Property="RenderTransformOrigin" Value="50%,100%" />
            </Style>

            <Style Selector="^:indeterminate">
                <!-- <Style Selector="^ /template/ Popup#PART_Popup"> -->
                <!--     <Setter Property="IsOpen" Value="False" /> -->
                <!-- </Style> -->

                <Style Selector="^ /template/ Panel#PART_Determinate">
                    <Setter Property="Opacity" Value="0.0" />
                </Style>
                <Style Selector="^ /template/ Panel#PART_Indeterminate">
                    <Setter Property="Opacity" Value="1.0" />
                </Style>
                <Style Selector="^ /template/ Border#PART_Runner">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="33%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
                <Style Selector="^ /template/ Border#PART_Chaser">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite" Easing="SineEaseOut">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="33%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="0" />
                            </KeyFrame>
                            <KeyFrame Cue="66%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
                <Style Selector="^ /template/ Panel#PART_Indeterminate">
                    <Style.Animations>
                        <Animation Duration="0:0:3" IterationCount="Infinite" Easing="CubicEaseInOut">
                            <KeyFrame Cue="0%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="50%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="1" />
                            </KeyFrame>
                            <KeyFrame Cue="100%">
                                <Setter Property="ScaleTransform.ScaleY"
                                        Value="0" />
                            </KeyFrame>
                        </Animation>
                    </Style.Animations>
                </Style>
            </Style>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="BorderedProgressBarTheme" TargetType="ProgressBar"
                  BasedOn="{StaticResource {x:Type ProgressBar}}">
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="2" />
        <Setter Property="Padding" Value="1" />

        <Style Selector="^:horizontal">
            <Setter Property="MinHeight" Value="10" />
        </Style>

        <Style Selector="^:vertical">
            <Setter Property="MinWidth" Value="10" />
        </Style>
    </ControlTheme>
</ResourceDictionary>

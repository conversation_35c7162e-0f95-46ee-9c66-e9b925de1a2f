<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia">
    <Design.PreviewWith>
        <Panel>
            <StackPanel>
                <husk:IconLabel Icon="Home" Text="Home" Foreground="Aqua" FontSize="24" FontWeight="Bold" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type husk:IconLabel}" TargetType="husk:IconLabel">
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*" ColumnSpacing="{TemplateBinding Spacing}">
                    <fi:SymbolIcon Grid.Column="0" Symbol="{TemplateBinding Icon}"
                                   IconVariant="{TemplateBinding Variant}"
                                   FontSize="{TemplateBinding FontSize}" Foreground="{TemplateBinding Foreground}" />
                    <TextBlock Grid.Column="1" Text="{TemplateBinding Text}" FontSize="{TemplateBinding FontSize}"
                               Foreground="{TemplateBinding Foreground}" />
                </Grid>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>

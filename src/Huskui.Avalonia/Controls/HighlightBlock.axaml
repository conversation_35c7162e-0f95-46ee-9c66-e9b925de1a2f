<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Spacing="12" Orientation="Horizontal">
                <TextBlock>
                    <local:HighlightBlock Text="Ctrl+C" Classes="Shortcut" />
                    <Run Text="and" />
                    <local:HighlightBlock Text="Ctrl+C" Classes="Primary Shortcut" />
                    <Run Text="then" />
                    <local:HighlightBlock Text="✨ git push -f" />
                    <Run Text="or" />
                    <local:HighlightBlock Text="✨ git push -f" Classes="Primary" />
                    <Run Text="!" />
                </TextBlock>
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type local:HighlightBlock}" TargetType="local:HighlightBlock">
        <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource ControlSecondaryForegroundBrush}" />
        <Setter Property="Padding" Value="6,0" />
        <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
        <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}"
                        Padding="{TemplateBinding Padding}">
                    <TextPresenter Text="{TemplateBinding Text}" VerticalAlignment="Center" />
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^.Shortcut">
            <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
            <Setter Property="BorderThickness" Value="1,1,1,2" />
        </Style>

        <Style Selector="^.Primary">
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentFullBackgroundBrush}" />
            <Setter Property="Foreground" Value="{StaticResource ControlAccentForegroundBrush}" />
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>

<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:fi="using:FluentIcons.Avalonia"
                    xmlns:local="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White" Width="256">
            <StackPanel Spacing="20" Margin="24">
                <TextBox Text="Test 测试 Test 0123456789" Watermark="12" UseFloatingWatermark="True">
                    <TextBox.InnerLeftContent>
                        <StackPanel Orientation="Horizontal">
                            <fi:SymbolIcon Margin="8" IconVariant="Filled"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Symbol="People" FontSize="{StaticResource MediumFontSize}" />
                            <local:Divider Orientation="Vertical" />
                        </StackPanel>
                    </TextBox.InnerLeftContent>
                    <TextBox.InnerRightContent>
                        <StackPanel Orientation="Horizontal">
                            <Button>
                                <fi:SymbolIcon Symbol="Pen" FontSize="{StaticResource SmallFontSize}" />
                            </Button>
                            <Button>
                                <fi:SymbolIcon Symbol="Pen" FontSize="{StaticResource SmallFontSize}" />
                            </Button>
                        </StackPanel>
                    </TextBox.InnerRightContent>
                </TextBox>
                <TextBox Watermark="Disabled" IsEnabled="False" />
                <TextBox Watermark="Enabled" IsEnabled="True" UseFloatingWatermark="True" />
                <TextBox Theme="{StaticResource UnderlineTextBoxTheme}" Watermark="Enabled" IsEnabled="True"
                         UseFloatingWatermark="True" />
                <TextBox Theme="{StaticResource FieldTextBoxTheme}" Watermark="Enabled" IsEnabled="True"
                         UseFloatingWatermark="True" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>
    <ControlTheme x:Key="{x:Type TextBox}" TargetType="TextBox">
        <!-- <Setter Property="Cursor" Value="Ibeam" /> -->
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Background" Value="{StaticResource OverlayInteractiveBackgroundBrush}" />
        <Setter Property="SelectionBrush" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="SelectionForegroundBrush" Value="{StaticResource ControlReversedForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="CaretBrush" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border x:Name="PART_Border" BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}" CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                        <Grid ColumnDefinitions="Auto,0,*,0,Auto">
                            <Grid.Styles>
                                <Style Selector="ContentPresenter Button">
                                    <Setter Property="Theme" Value="{StaticResource GhostButtonTheme}" />
                                    <Setter Property="Padding" Value="8,4" />
                                    <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                                    <Setter Property="Margin" Value="2" />
                                </Style>
                            </Grid.Styles>
                            <ContentPresenter Grid.Column="0" Content="{TemplateBinding InnerLeftContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <ScrollViewer Grid.Column="2"
                                          Cursor="Ibeam"
                                          AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                                          HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                          IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                                          VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                                          BringIntoViewOnFocusChange="{TemplateBinding (ScrollViewer.BringIntoViewOnFocusChange)}">
                                <Panel Margin="{TemplateBinding Padding}">
                                    <TextBlock
                                        Name="PART_Watermark"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Opacity="0.5"
                                        Text="{TemplateBinding Watermark}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}">
                                        <TextBlock.IsVisible>
                                            <MultiBinding Converter="{x:Static BoolConverters.And}">
                                                <Binding ElementName="PART_TextPresenter" Path="PreeditText"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                                <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Text"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                            </MultiBinding>
                                        </TextBlock.IsVisible>
                                    </TextBlock>
                                    <TextPresenter
                                        Name="PART_TextPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        CaretBlinkInterval="{TemplateBinding CaretBlinkInterval}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        CaretBrush="{TemplateBinding CaretBrush}"
                                        CaretIndex="{TemplateBinding CaretIndex}"
                                        LineHeight="{TemplateBinding LineHeight}"
                                        PasswordChar="{TemplateBinding PasswordChar}"
                                        RevealPassword="{TemplateBinding RevealPassword}"
                                        SelectionBrush="{TemplateBinding SelectionBrush}"
                                        SelectionEnd="{TemplateBinding SelectionEnd}"
                                        SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                        SelectionStart="{TemplateBinding SelectionStart}"
                                        Text="{TemplateBinding Text, Mode=TwoWay}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}" />
                                </Panel>
                            </ScrollViewer>
                            <ContentPresenter Grid.Column="4" Content="{TemplateBinding InnerRightContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </Grid>
                    </Border>
                    <Border x:Name="PART_GlowBorder" CornerRadius="{TemplateBinding CornerRadius}"
                            BorderBrush="{StaticResource TransparentBrush}" BorderThickness="2">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:focus /template/ Border#PART_GlowBorder">
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
        </Style>

        <Style Selector="^:focus /template/ Border#PART_Border">
            <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        </Style>

        <Style Selector="^:pointerover:not(:focus) /template/ Border#PART_Border">
            <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Border#PART_Border">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>

        <Style Selector="^[UseFloatingWatermark=true]:not(:empty) /template/ TextBlock#PART_FloatingWatermark">
            <Setter Property="IsVisible" Value="True" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="UnderlineTextBoxTheme" TargetType="TextBox">
        <Setter Property="Cursor" Value="Ibeam" />
        <Setter Property="Background" Value="{StaticResource OverlayInteractiveBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlInteractiveBorderBrush}" />
        <Setter Property="BorderThickness" Value="1,1,1,2" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="SelectionBrush" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="SelectionForegroundBrush" Value="{StaticResource ControlReversedForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="CaretBrush" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Root">
                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                        <Grid ColumnDefinitions="Auto,0,*,0,Auto">
                            <Grid.Styles>
                                <Style Selector="ContentPresenter Button">
                                    <Setter Property="Theme" Value="{StaticResource GhostButtonTheme}" />
                                    <Setter Property="Padding" Value="8,4" />
                                    <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                                    <Setter Property="Margin" Value="2" />
                                </Style>
                            </Grid.Styles>
                            <ContentPresenter Grid.Column="0" Content="{TemplateBinding InnerLeftContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <ScrollViewer Grid.Column="2"
                                          AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                                          HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                          IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                                          VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                                          BringIntoViewOnFocusChange="{TemplateBinding (ScrollViewer.BringIntoViewOnFocusChange)}">
                                <Panel Margin="{TemplateBinding Padding}">
                                    <TextBlock
                                        Name="PART_Watermark"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Opacity="0.5"
                                        Text="{TemplateBinding Watermark}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}">
                                        <TextBlock.IsVisible>
                                            <MultiBinding Converter="{x:Static BoolConverters.And}">
                                                <Binding ElementName="PART_TextPresenter" Path="PreeditText"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                                <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Text"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                            </MultiBinding>
                                        </TextBlock.IsVisible>
                                    </TextBlock>
                                    <TextPresenter
                                        Name="PART_TextPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        CaretBlinkInterval="{TemplateBinding CaretBlinkInterval}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        CaretBrush="{TemplateBinding CaretBrush}"
                                        CaretIndex="{TemplateBinding CaretIndex}"
                                        LineHeight="{TemplateBinding LineHeight}"
                                        PasswordChar="{TemplateBinding PasswordChar}"
                                        RevealPassword="{TemplateBinding RevealPassword}"
                                        SelectionBrush="{TemplateBinding SelectionBrush}"
                                        SelectionEnd="{TemplateBinding SelectionEnd}"
                                        SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                        SelectionStart="{TemplateBinding SelectionStart}"
                                        Text="{TemplateBinding Text, Mode=TwoWay}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}" />
                                </Panel>
                            </ScrollViewer>
                            <ContentPresenter Grid.Column="4" Content="{TemplateBinding InnerRightContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </Grid>
                    </Border>
                    <Border x:Name="Indicator" CornerRadius="{TemplateBinding CornerRadius}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="0,0,0,2" RenderTransform="scaleX(0.0)">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseOut" />
                                <TransformOperationsTransition Property="RenderTransform"
                                                               Duration="{StaticResource ControlNormalAnimationDuration}"
                                                               Easing="CubicEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:focus /template/ Border#Indicator">
            <Setter Property="BorderBrush" Value="{StaticResource ControlAccentInteractiveBorderBrush}" />
            <Setter Property="RenderTransform" Value="scaleX(1.0)" />
        </Style>

        <Style Selector="^:focus /template/ Border#Root">
            <Setter Property="Background" Value="{StaticResource OverlaySolidBackgroundBrush}" />
        </Style>

        <Style Selector="^:disabled /template/ Panel#Root">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="FieldTextBoxTheme" TargetType="TextBox">
        <Setter Property="Cursor" Value="Ibeam" />
        <Setter Property="Background" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource ControlTranslucentFullBackgroundBrush}" />
        <Setter Property="BorderThickness" Value="1,1,1,1" />
        <Setter Property="Foreground" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="SelectionBrush" Value="{StaticResource ControlAccentInteractiveBackgroundBrush}" />
        <Setter Property="SelectionForegroundBrush" Value="{StaticResource ControlReversedForegroundBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="CaretBrush" Value="{StaticResource ControlForegroundBrush}" />
        <Setter Property="FontSize" Value="{StaticResource MediumFontSize}" />
        <Setter Property="Padding" Value="12,8" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="Root">
                    <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background"
                                                 Duration="{StaticResource ControlFasterAnimationDuration}"
                                                 Easing="SineEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                        <Grid ColumnDefinitions="Auto,0,*,0,Auto">
                            <Grid.Styles>
                                <Style Selector="ContentPresenter Button">
                                    <Setter Property="Theme" Value="{StaticResource GhostButtonTheme}" />
                                    <Setter Property="Padding" Value="8,4" />
                                    <Setter Property="FontSize" Value="{StaticResource SmallFontSize}" />
                                    <Setter Property="Margin" Value="2" />
                                </Style>
                            </Grid.Styles>
                            <ContentPresenter Grid.Column="0" Content="{TemplateBinding InnerLeftContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <ScrollViewer Grid.Column="2"
                                          AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                                          HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                          IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                                          VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                                          BringIntoViewOnFocusChange="{TemplateBinding (ScrollViewer.BringIntoViewOnFocusChange)}">
                                <Panel Margin="{TemplateBinding Padding}">
                                    <TextBlock
                                        Name="PART_Watermark"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        Opacity="0.5"
                                        Text="{TemplateBinding Watermark}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}">
                                        <TextBlock.IsVisible>
                                            <MultiBinding Converter="{x:Static BoolConverters.And}">
                                                <Binding ElementName="PART_TextPresenter" Path="PreeditText"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                                <Binding RelativeSource="{RelativeSource TemplatedParent}" Path="Text"
                                                         Converter="{x:Static StringConverters.IsNullOrEmpty}" />
                                            </MultiBinding>
                                        </TextBlock.IsVisible>
                                    </TextBlock>
                                    <TextPresenter
                                        Name="PART_TextPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        CaretBlinkInterval="{TemplateBinding CaretBlinkInterval}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        CaretBrush="{TemplateBinding CaretBrush}"
                                        CaretIndex="{TemplateBinding CaretIndex}"
                                        LineHeight="{TemplateBinding LineHeight}"
                                        PasswordChar="{TemplateBinding PasswordChar}"
                                        RevealPassword="{TemplateBinding RevealPassword}"
                                        SelectionBrush="{TemplateBinding SelectionBrush}"
                                        SelectionEnd="{TemplateBinding SelectionEnd}"
                                        SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                        SelectionStart="{TemplateBinding SelectionStart}"
                                        Text="{TemplateBinding Text, Mode=TwoWay}"
                                        TextAlignment="{TemplateBinding TextAlignment}"
                                        TextWrapping="{TemplateBinding TextWrapping}" />
                                </Panel>
                            </ScrollViewer>
                            <ContentPresenter Grid.Column="4" Content="{TemplateBinding InnerRightContent}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                        </Grid>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Panel#Root">
            <Setter Property="Opacity" Value="{StaticResource ControlDimOpacity}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>
